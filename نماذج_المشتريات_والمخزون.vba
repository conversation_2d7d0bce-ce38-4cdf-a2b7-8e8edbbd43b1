' كود VBA لإنشاء نماذج المشتريات والمخزون
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء نماذج المشتريات والمخزون
Public Sub انشاء_نماذج_المشتريات_والمخزون()
    On Error GoTo ErrorHandler
    
    ' إنشاء نماذج المشتريات
    Call انشاء_نموذج_فواتير_المشتريات
    Call انشاء_نموذج_تفاصيل_فواتير_المشتريات
    
    ' إنشاء نماذج المخزون
    Call انشاء_نموذج_ارصدة_المواد_الخام
    Call انشاء_نموذج_ارصدة_المنتجات_التامة
    Call انشاء_نموذج_حركات_المخزون
    
    MsgBox "تم إنشاء نماذج المشتريات والمخزون بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النماذج: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء نموذج فواتير المشتريات
Private Sub انشاء_نموذج_فواتير_المشتريات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_فواتير_المشتريات"
    frm.Caption = "فواتير المشتريات"
    frm.RecordSource = "فواتير_المشتريات"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 10000, 600)
    ctl.Caption = "فواتير المشتريات"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' رقم الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "رقم الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "رقم_الفاتورة"
    ctl.Enabled = False
    
    ' المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "المورد:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "كود_المورد"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_المورد, اسم_المورد FROM الموردين WHERE حالة_المورد = 'نشط' ORDER BY اسم_المورد"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3000"
    ctl.BoundColumn = 1
    
    ' تاريخ الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6000, 1000, 1800, 300)
    ctl.Caption = "تاريخ الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7900, 1000, 1500, 300)
    ctl.ControlSource = "تاريخ_الفاتورة"
    ctl.Format = "Short Date"
    
    ' رقم فاتورة المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6000, 1400, 1800, 300)
    ctl.Caption = "رقم فاتورة المورد:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7900, 1400, 1500, 300)
    ctl.ControlSource = "رقم_فاتورة_المورد"
    
    ' إجمالي الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "إجمالي الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1800, 1500, 300)
    ctl.ControlSource = "اجمالي_الفاتورة"
    ctl.Format = "Currency"
    
    ' الخصم
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4200, 1800, 1200, 300)
    ctl.Caption = "الخصم:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 5500, 1800, 1200, 300)
    ctl.ControlSource = "الخصم"
    ctl.Format = "Currency"
    
    ' الضريبة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7000, 1800, 1200, 300)
    ctl.Caption = "الضريبة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 8300, 1800, 1200, 300)
    ctl.ControlSource = "الضريبة"
    ctl.Format = "Currency"
    
    ' صافي الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "صافي الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 1500, 300)
    ctl.ControlSource = "صافي_الفاتورة"
    ctl.Format = "Currency"
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 255, 200)
    
    ' حالة الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4200, 2200, 1500, 300)
    ctl.Caption = "حالة الفاتورة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 5800, 2200, 1500, 300)
    ctl.ControlSource = "حالة_الفاتورة"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "مؤكدة;مؤجلة;ملغية"
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 6000, 600)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' نموذج فرعي لتفاصيل الفاتورة
    Set ctl = CreateControl(frm.Name, acSubform, , , , 500, 3400, 8500, 3000)
    ctl.SourceObject = "نموذج_تفاصيل_فواتير_المشتريات"
    ctl.LinkChildFields = "رقم_الفاتورة"
    ctl.LinkMasterFields = "رقم_الفاتورة"
    ctl.Name = "subform_تفاصيل_الفاتورة"
    
    ' أزرار التحكم
    Call اضافة_ازرار_فواتير_المشتريات(frm, 6600)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج تفاصيل فواتير المشتريات
Private Sub انشاء_نموذج_تفاصيل_فواتير_المشتريات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_تفاصيل_فواتير_المشتريات"
    frm.Caption = "تفاصيل فواتير المشتريات"
    frm.RecordSource = "تفاصيل_فواتير_المشتريات"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = False
    
    ' إضافة الحقول كأعمدة في النموذج الفرعي
    ' سيتم عرضها في شكل جدول
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج أرصدة المواد الخام
Private Sub انشاء_نموذج_ارصدة_المواد_الخام()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_ارصدة_المواد_الخام"
    frm.Caption = "أرصدة المواد الخام"
    frm.RecordSource = "عرض_ارصدة_المواد_الخام"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 10000, 600)
    ctl.Caption = "أرصدة المواد الخام الحالية"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج أرصدة المنتجات التامة
Private Sub انشاء_نموذج_ارصدة_المنتجات_التامة()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_ارصدة_المنتجات_التامة"
    frm.Caption = "أرصدة المنتجات التامة"
    frm.RecordSource = "عرض_ارصدة_المنتجات_التامة"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 10000, 600)
    ctl.Caption = "أرصدة المنتجات التامة الحالية"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج حركات المخزون
Private Sub انشاء_نموذج_حركات_المخزون()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_حركات_المخزون"
    frm.Caption = "حركات المخزون"
    frm.RecordSource = "حركات_المخزون"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 10000, 600)
    ctl.Caption = "سجل حركات المخزون"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إجراء إضافة أزرار التحكم لفواتير المشتريات
Private Sub اضافة_ازرار_فواتير_المشتريات(frm As Form, yPosition As Long)
    Dim ctl As Control
    
    ' زر فاتورة جديدة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 500, yPosition, 1500, 400)
    ctl.Caption = "فاتورة جديدة"
    ctl.Name = "btn_فاتورة_جديدة"
    
    ' زر حفظ الفاتورة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 2100, yPosition, 1500, 400)
    ctl.Caption = "حفظ الفاتورة"
    ctl.Name = "btn_حفظ_الفاتورة"
    
    ' زر تأكيد الفاتورة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3700, yPosition, 1500, 400)
    ctl.Caption = "تأكيد الفاتورة"
    ctl.Name = "btn_تاكيد_الفاتورة"
    
    ' زر طباعة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 5300, yPosition, 1200, 400)
    ctl.Caption = "طباعة"
    ctl.Name = "btn_طباعة"
    
    ' زر إغلاق
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 6600, yPosition, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btn_اغلاق"
End Sub
