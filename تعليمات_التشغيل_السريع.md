# تعليمات التشغيل السريع - نظام محاسبة التكاليف

## خطوات التشغيل الأولى

### 1. إنشاء النظام
```
1. شغل ملف: تشغيل_النظام_الكامل.vbs
2. انتظر حتى يتم إنشاء ملف Excel
3. افتح الملف: نظام_محاسبة_التكاليف_متكامل.xlsx
```

### 2. إعداد البيانات الأساسية (مرة واحدة فقط)

#### أ. إعداد الموردين
- انتقل إلى ورقة "الموردين"
- أدخل بيانات الموردين:
  ```
  SUP001 | شركة الزيتون الذهبي | عمان | 06-1234567
  SUP002 | مؤسسة الجوز الطبيعي | دمشق | 011-2345678
  SUP003 | شركة اللبنة الطازجة | بيروت | 01-345678
  ```

#### ب. إعداد العملاء
- انتقل إلى ورقة "العملاء"
- أدخل بيانات العملاء:
  ```
  CUS001 | سوبر ماركت الأمل | عمان | 06-7654321
  CUS002 | مطعم الأصالة | دبي | 04-8765432
  ```

#### ج. إعداد المواد الخام
- انتقل إلى ورقة "المواد_الخام"
- أدخل المواد الأساسية:
  ```
  RM001 | زيتون أخضر | كيلوغرام | 5.5 | 100 | SUP001
  RM002 | لبنة طبيعية | كيلوغرام | 8.0 | 50 | SUP003
  RM003 | جوز مقشر | كيلوغرام | 15.0 | 25 | SUP002
  RM004 | عين الجمل | كيلوغرام | 18.0 | 20 | SUP002
  RM005 | دقيق أبيض | كيلوغرام | 2.5 | 200 | SUP001
  RM006 | سمن نباتي | كيلوغرام | 4.0 | 50 | SUP001
  RM007 | سكر ناعم | كيلوغرام | 3.0 | 100 | SUP001
  ```

#### د. إعداد المنتجات التامة
- انتقل إلى ورقة "المنتجات_التامة"
- أدخل المنتجات:
  ```
  FG001 | زيتون أخضر مشوي باللبنة | علبة 500 غرام | 12.0 | 30
  FG002 | معمول بالجوز | علبة 250 غرام | 8.5 | 15
  FG003 | معمول بعين الجمل | علبة 250 غرام | 9.0 | 15
  ```

#### هـ. إعداد الوصفات
- انتقل إلى ورقة "الوصفات"
- أدخل وصفات المنتجات:

**وصفة الزيتون الأخضر المشوي باللبنة (FG001):**
```
FG001 | RM001 | زيتون أخضر | 0.3 | كيلوغرام
FG001 | RM002 | لبنة طبيعية | 0.2 | كيلوغرام
```

**وصفة المعمول بالجوز (FG002):**
```
FG002 | RM005 | دقيق أبيض | 0.15 | كيلوغرام
FG002 | RM003 | جوز مقشر | 0.08 | كيلوغرام
FG002 | RM006 | سمن نباتي | 0.02 | كيلوغرام
FG002 | RM007 | سكر ناعم | 0.01 | كيلوغرام
```

**وصفة المعمول بعين الجمل (FG003):**
```
FG003 | RM005 | دقيق أبيض | 0.15 | كيلوغرام
FG003 | RM004 | عين الجمل | 0.08 | كيلوغرام
FG003 | RM006 | سمن نباتي | 0.02 | كيلوغرام
FG003 | RM007 | سكر ناعم | 0.01 | كيلوغرام
```

## العمليات اليومية

### 1. تسجيل المشتريات
```
1. انتقل إلى ورقة "المشتريات"
2. أدخل تفاصيل الشراء:
   - رقم الفاتورة
   - التاريخ
   - كود المورد
   - كود المادة
   - الكمية
   - سعر الوحدة
3. سيتم حساب إجمالي القيمة تلقائياً
4. شغل ماكرو "تحديث_مخزون_المواد_الخام"
```

### 2. إنشاء أمر إنتاج
```
1. انتقل إلى ورقة "اوامر_الانتاج"
2. أدخل تفاصيل الأمر:
   - رقم الأمر (PRO001, PRO002, ...)
   - التاريخ
   - كود المنتج
   - اسم المنتج
   - الكمية المطلوبة
   - تكلفة العمالة
   - التكاليف الإضافية
   - الحالة: "قيد التنفيذ"
3. شغل ماكرو "حساب_تكلفة_الانتاج"
4. عند اكتمال الإنتاج، غير الحالة إلى "مكتمل"
5. شغل ماكرو "تحديث_مخزون_المنتجات_التامة"
```

### 3. تسجيل المبيعات
```
1. انتقل إلى ورقة "المبيعات"
2. أدخل تفاصيل البيع:
   - رقم الفاتورة
   - التاريخ
   - كود العميل
   - كود المنتج
   - اسم المنتج
   - الكمية
   - سعر الوحدة
3. سيتم حساب إجمالي المبيعات تلقائياً
4. شغل ماكرو "تحديث_مخزون_بعد_المبيعات"
```

## الماكرو المتاح

### 1. ماكرو التحديث التلقائي
```
تحديث_مخزون_المواد_الخام() - يحدث مخزون المواد الخام بعد المشتريات
حساب_تكلفة_الانتاج() - يحسب تكلفة الإنتاج حسب الوصفات
تحديث_مخزون_المنتجات_التامة() - يحدث مخزون المنتجات بعد الإنتاج
تحديث_مخزون_بعد_المبيعات() - يحدث المخزون بعد المبيعات
تحديث_شامل_للنظام() - يشغل جميع الماكرو السابقة
```

### 2. كيفية تشغيل الماكرو
```
1. اضغط Alt + F11 لفتح محرر VBA
2. اضغط F5 أو اختر Run > Run Sub/UserForm
3. أو استخدم Developer Tab > Macros
```

## التقارير اليومية

### 1. تقرير المخزون
- انتقل إلى ورقة "تقرير_المخزون"
- راجع أرصدة المواد الخام والمنتجات التامة
- تحقق من المواد منخفضة المخزون

### 2. تقرير التكاليف
- انتقل إلى ورقة "تقرير_التكاليف"
- راجع تكلفة إنتاج كل منتج
- قارن التكاليف الفعلية بالمخططة

### 3. التقارير المالية
- انتقل إلى ورقة "التقارير_المالية"
- راجع قائمة الأرباح والخسائر
- تحليل الربحية بالمنتج

## نصائح سريعة

### 1. الاختصارات المفيدة
```
Ctrl + Home - الانتقال إلى بداية الورقة
Ctrl + End - الانتقال إلى آخر خلية مستخدمة
Ctrl + PageUp/PageDown - التنقل بين الأوراق
F2 - تحرير الخلية
F9 - إعادة حساب الصيغ
```

### 2. التحقق من صحة البيانات
```
- تأكد من صحة أكواد المواد والمنتجات
- راجع الكميات والأسعار قبل الحفظ
- تحقق من تطابق البيانات بين الأوراق المختلفة
```

### 3. النسخ الاحتياطي
```
- احفظ نسخة احتياطية يومياً
- استخدم أسماء ملفات تحتوي على التاريخ
- مثال: نظام_التكاليف_2024_01_15.xlsx
```

## حل المشاكل الشائعة

### 1. خطأ في الصيغ
```
المشكلة: #REF! أو #VALUE!
الحل: تحقق من صحة مراجع الخلايا والبيانات
```

### 2. عدم تحديث المخزون
```
المشكلة: المخزون لا يتحدث تلقائياً
الحل: شغل الماكرو المناسب يدوياً
```

### 3. بطء في الأداء
```
المشكلة: Excel يعمل ببطء
الحل: 
- أغلق الملفات غير المستخدمة
- احذف البيانات القديمة غير المطلوبة
- استخدم Application.ScreenUpdating = False في الماكرو
```

## جدولة المهام

### يومياً
- [ ] تسجيل المشتريات
- [ ] تسجيل المبيعات
- [ ] تحديث المخزون
- [ ] مراجعة التقارير

### أسبوعياً
- [ ] مراجعة أرصدة المخزون
- [ ] تحليل التكاليف
- [ ] مراجعة الوصفات

### شهرياً
- [ ] إعداد التقارير المالية
- [ ] تحليل الربحية
- [ ] مراجعة الأسعار
- [ ] النسخ الاحتياطي الشامل

---

**للدعم الفني**: راجع دليل المستخدم الكامل أو اتصل بمطور النظام.
