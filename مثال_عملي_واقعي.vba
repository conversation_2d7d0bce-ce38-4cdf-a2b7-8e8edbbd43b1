' مثال عملي واقعي لنظام محاسبة التكاليف
' شركة أبو فرح للمواد الغذائية - مثال شامل

Sub تطبيق_مثال_عملي_واقعي()
    Application.ScreenUpdating = False
    
    MsgBox "سيتم الآن تطبيق مثال عملي واقعي لشركة أبو فرح للمواد الغذائية" & vbCrLf & _
           "هذا المثال يتضمن:" & vbCrLf & _
           "• بيانات موردين حقيقيين" & vbCrLf & _
           "• عملاء فعليين" & vbCrLf & _
           "• مشتريات شهر كامل" & vbCrLf & _
           "• دورة إنتاج كاملة" & vbCrLf & _
           "• مبيعات وتقارير", vbInformation, "مثال عملي واقعي"
    
    ' تطبيق البيانات الأساسية
    Call ادخال_بيانات_الموردين_الواقعية
    Call ادخال_بيانات_العملاء_الواقعية
    Call ادخال_بيانات_المواد_الخام_الواقعية
    Call ادخال_بيانات_المنتجات_الواقعية
    Call ادخال_الوصفات_التفصيلية
    
    ' تطبيق العمليات الشهرية
    Call تطبيق_مشتريات_شهر_كامل
    Call تطبيق_دورة_انتاج_كاملة
    Call تطبيق_مبيعات_شهر_كامل
    
    ' إنشاء التقارير
    Call انشاء_تقارير_شاملة
    
    ' فحص الشيتات غير المكتملة
    Call فحص_الشيتات_غير_المكتملة
    
    Application.ScreenUpdating = True
    
    MsgBox "تم تطبيق المثال العملي بنجاح!" & vbCrLf & _
           "يمكنك الآن مراجعة جميع الأوراق والتقارير", vbInformation, "مثال مكتمل"
End Sub

Sub ادخال_بيانات_الموردين_الواقعية()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("الموردين")
    
    ' تنظيف البيانات السابقة
    ws.Range("A4:F100").ClearContents
    
    With ws
        ' مورد الزيتون - شركة الزيتون الذهبي
        .Range("A4").Value = "SUP001"
        .Range("B4").Value = "شركة الزيتون الذهبي المحدودة"
        .Range("C4").Value = "عمان - الجبيهة - شارع الملكة رانيا"
        .Range("D4").Value = "06-5234567"
        .Range("E4").Value = "<EMAIL>"
        .Range("F4").Value = "أحمد محمد الزيتوني"
        
        ' مورد اللبنة - مزارع الأردن
        .Range("A5").Value = "SUP002"
        .Range("B5").Value = "مزارع الأردن للألبان"
        .Range("C5").Value = "إربد - الرمثا - المنطقة الصناعية"
        .Range("D5").Value = "02-7345678"
        .Range("E5").Value = "<EMAIL>"
        .Range("F5").Value = "فاطمة أحمد الحليب"
        
        ' مورد الجوز - شركة المكسرات الطبيعية
        .Range("A6").Value = "SUP003"
        .Range("B6").Value = "شركة المكسرات الطبيعية"
        .Range("C6").Value = "عجلون - عين جنا - طريق الملك عبدالله"
        .Range("D6").Value = "02-6456789"
        .Range("E6").Value = "<EMAIL>"
        .Range("F6").Value = "محمد سالم الجوزي"
        
        ' مورد الدقيق - مطاحن الأردن
        .Range("A7").Value = "SUP004"
        .Range("B7").Value = "مطاحن الأردن الكبرى"
        .Range("C7").Value = "الزرقاء - المنطقة الصناعية الأولى"
        .Range("D7").Value = "05-3567890"
        .Range("E7").Value = "<EMAIL>"
        .Range("F7").Value = "خالد عبدالله الطحان"
        
        ' مورد السمن والسكر - شركة الغذاء المتكامل
        .Range("A8").Value = "SUP005"
        .Range("B8").Value = "شركة الغذاء المتكامل"
        .Range("C8").Value = "عمان - ماركا الشمالية - شارع الصناعة"
        .Range("D8").Value = "06-4678901"
        .Range("E8").Value = "<EMAIL>"
        .Range("F8").Value = "نادية محمود الحلو"
    End With
End Sub

Sub ادخال_بيانات_العملاء_الواقعية()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("العملاء")
    
    ws.Range("A4:G100").ClearContents
    
    With ws
        ' سوبر ماركت الأمل
        .Range("A4").Value = "CUS001"
        .Range("B4").Value = "سوبر ماركت الأمل"
        .Range("C4").Value = "عمان - الدوار السابع - مجمع الأمل التجاري"
        .Range("D4").Value = "06-7654321"
        .Range("E4").Value = "<EMAIL>"
        .Range("F4").Value = "تجزئة"
        .Range("G4").Value = "ممتاز"
        
        ' مخابز ومعجنات الفردوس
        .Range("A5").Value = "CUS002"
        .Range("B5").Value = "مخابز ومعجنات الفردوس"
        .Range("C5").Value = "إربد - حي الحصن - شارع الجامعة"
        .Range("D5").Value = "02-8765432"
        .Range("E5").Value = "<EMAIL>"
        .Range("F5").Value = "جملة"
        .Range("G5").Value = "جيد"
        
        ' مطعم الأصالة
        .Range("A6").Value = "CUS003"
        .Range("B6").Value = "مطعم الأصالة للمأكولات الشعبية"
        .Range("C6").Value = "عمان - وسط البلد - شارع الملك فيصل"
        .Range("D6").Value = "06-9876543"
        .Range("E6").Value = "<EMAIL>"
        .Range("F6").Value = "مطاعم"
        .Range("G6").Value = "ممتاز"
        
        ' جمعية خيرية الرحمة
        .Range("A7").Value = "CUS004"
        .Range("B7").Value = "جمعية خيرية الرحمة"
        .Range("C7").Value = "الزرقاء - حي الأمير راشد - شارع الخدمات"
        .Range("D7").Value = "05-1234567"
        .Range("E7").Value = "<EMAIL>"
        .Range("F7").Value = "جمعيات"
        .Range("G7").Value = "ممتاز"
        
        ' كافتيريا الجامعة
        .Range("A8").Value = "CUS005"
        .Range("B8").Value = "كافتيريا الجامعة الأردنية"
        .Range("C8").Value = "عمان - الجبيهة - الجامعة الأردنية"
        .Range("D8").Value = "06-5353535"
        .Range("E8").Value = "<EMAIL>"
        .Range("F8").Value = "مؤسسات"
        .Range("G8").Value = "جيد"
    End With
End Sub

Sub ادخال_بيانات_المواد_الخام_الواقعية()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المواد_الخام")
    
    ws.Range("A4:G100").ClearContents
    
    With ws
        ' زيتون أخضر
        .Range("A4").Value = "RM001"
        .Range("B4").Value = "زيتون أخضر طازج - درجة أولى"
        .Range("C4").Value = "كيلوغرام"
        .Range("D4").Value = 6.5  ' السعر الحالي
        .Range("E4").Value = 50   ' الحد الأدنى
        .Range("F4").Value = "SUP001"
        .Range("G4").Value = "مبرد 2-4 درجة مئوية"
        
        ' لبنة طبيعية
        .Range("A5").Value = "RM002"
        .Range("B5").Value = "لبنة طبيعية طازجة - دسم كامل"
        .Range("C5").Value = "كيلوغرام"
        .Range("D5").Value = 9.0
        .Range("E5").Value = 30
        .Range("F5").Value = "SUP002"
        .Range("G5").Value = "مبرد 0-2 درجة مئوية"
        
        ' جوز مقشر
        .Range("A6").Value = "RM003"
        .Range("B6").Value = "جوز مقشر - أنصاف - درجة أولى"
        .Range("C6").Value = "كيلوغرام"
        .Range("D6").Value = 18.0
        .Range("E6").Value = 15
        .Range("F6").Value = "SUP003"
        .Range("G6").Value = "جاف ومظلم"
        
        ' عين الجمل
        .Range("A7").Value = "RM004"
        .Range("B7").Value = "عين الجمل مقشر - أنصاف"
        .Range("C7").Value = "كيلوغرام"
        .Range("D7").Value = 22.0
        .Range("E7").Value = 10
        .Range("F7").Value = "SUP003"
        .Range("G7").Value = "جاف ومظلم"
        
        ' دقيق أبيض
        .Range("A8").Value = "RM005"
        .Range("B8").Value = "دقيق أبيض فاخر - صفر"
        .Range("C8").Value = "كيلوغرام"
        .Range("D8").Value = 0.85
        .Range("E8").Value = 200
        .Range("F8").Value = "SUP004"
        .Range("G8").Value = "جاف ومعتدل"
        
        ' سمن نباتي
        .Range("A9").Value = "RM006"
        .Range("B9").Value = "سمن نباتي صحي"
        .Range("C9").Value = "كيلوغرام"
        .Range("D9").Value = 3.2
        .Range("E9").Value = 25
        .Range("F9").Value = "SUP005"
        .Range("G9").Value = "درجة حرارة الغرفة"
        
        ' سكر ناعم
        .Range("A10").Value = "RM007"
        .Range("B10").Value = "سكر أبيض ناعم"
        .Range("C10").Value = "كيلوغرام"
        .Range("D10").Value = 1.1
        .Range("E10").Value = 100
        .Range("F10").Value = "SUP005"
        .Range("G10").Value = "جاف ومحكم الإغلاق"
    End With
End Sub

Sub ادخال_بيانات_المنتجات_الواقعية()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المنتجات_التامة")
    
    ws.Range("A4:F100").ClearContents
    
    With ws
        ' زيتون أخضر مشوي باللبنة
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "زيتون أخضر مشوي باللبنة - علبة 500 غرام"
        .Range("C4").Value = "علبة"
        .Range("D4").Value = 14.5  ' سعر البيع
        .Range("E4").Value = 15    ' مدة الصلاحية بالأيام
        .Range("F4").Value = "منتج مميز - طعم أصيل"
        
        ' معمول بالجوز
        .Range("A5").Value = "FG002"
        .Range("B5").Value = "معمول بالجوز الطبيعي - علبة 250 غرام"
        .Range("C5").Value = "علبة"
        .Range("D5").Value = 9.5
        .Range("E5").Value = 10
        .Range("F5").Value = "حلويات شرقية أصيلة"
        
        ' معمول بعين الجمل
        .Range("A6").Value = "FG003"
        .Range("B6").Value = "معمول بعين الجمل الفاخر - علبة 250 غرام"
        .Range("C6").Value = "علبة"
        .Range("D6").Value = 10.5
        .Range("E6").Value = 10
        .Range("F6").Value = "حلويات شرقية فاخرة"
    End With
End Sub

Sub ادخال_الوصفات_التفصيلية()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("الوصفات")
    
    ws.Range("A4:F100").ClearContents
    
    With ws
        ' وصفة الزيتون الأخضر المشوي باللبنة
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "RM001"
        .Range("C4").Value = "زيتون أخضر طازج"
        .Range("D4").Value = 0.35  ' كمية أكبر للحصول على 500 غرام نهائي
        .Range("E4").Value = "كيلوغرام"
        .Range("F4").Value = "يُشوى ويُتبل"
        
        .Range("A5").Value = "FG001"
        .Range("B5").Value = "RM002"
        .Range("C5").Value = "لبنة طبيعية طازجة"
        .Range("D5").Value = 0.25
        .Range("E5").Value = "كيلوغرام"
        .Range("F5").Value = "تُخلط مع التوابل"
        
        ' وصفة المعمول بالجوز
        .Range("A6").Value = "FG002"
        .Range("B6").Value = "RM005"
        .Range("C6").Value = "دقيق أبيض فاخر"
        .Range("D6").Value = 0.18
        .Range("E6").Value = "كيلوغرام"
        .Range("F6").Value = "للعجينة الخارجية"
        
        .Range("A7").Value = "FG002"
        .Range("B7").Value = "RM003"
        .Range("C7").Value = "جوز مقشر"
        .Range("D7").Value = 0.09
        .Range("E7").Value = "كيلوغرام"
        .Range("F7").Value = "للحشوة"
        
        .Range("A8").Value = "FG002"
        .Range("B8").Value = "RM006"
        .Range("C8").Value = "سمن نباتي"
        .Range("D8").Value = 0.025
        .Range("E8").Value = "كيلوغرام"
        .Range("F8").Value = "للعجينة"
        
        .Range("A9").Value = "FG002"
        .Range("B9").Value = "RM007"
        .Range("C9").Value = "سكر ناعم"
        .Range("D9").Value = 0.015
        .Range("E9").Value = "كيلوغرام"
        .Range("F9").Value = "للتحلية"
        
        ' وصفة المعمول بعين الجمل
        .Range("A10").Value = "FG003"
        .Range("B10").Value = "RM005"
        .Range("C10").Value = "دقيق أبيض فاخر"
        .Range("D10").Value = 0.18
        .Range("E10").Value = "كيلوغرام"
        .Range("F10").Value = "للعجينة الخارجية"
        
        .Range("A11").Value = "FG003"
        .Range("B11").Value = "RM004"
        .Range("C11").Value = "عين الجمل مقشر"
        .Range("D11").Value = 0.09
        .Range("E11").Value = "كيلوغرام"
        .Range("F11").Value = "للحشوة الفاخرة"
        
        .Range("A12").Value = "FG003"
        .Range("B12").Value = "RM006"
        .Range("C12").Value = "سمن نباتي"
        .Range("D12").Value = 0.025
        .Range("E12").Value = "كيلوغرام"
        .Range("F12").Value = "للعجينة"
        
        .Range("A13").Value = "FG003"
        .Range("B13").Value = "RM007"
        .Range("C13").Value = "سكر ناعم"
        .Range("D13").Value = 0.015
        .Range("E13").Value = "كيلوغرام"
        .Range("F13").Value = "للتحلية"
    End With
End Sub

Sub تطبيق_مشتريات_شهر_كامل()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المشتريات")
    
    ws.Range("A4:I100").ClearContents
    
    Dim currentDate As Date
    currentDate = DateSerial(2024, 1, 1)  ' بداية يناير 2024
    
    Dim row As Long
    row = 4
    
    With ws
        ' مشتريات الأسبوع الأول
        ' شراء زيتون أخضر
        .Cells(row, 1).Value = "PUR001"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "SUP001"
        .Cells(row, 4).Value = "RM001"
        .Cells(row, 5).Value = 150  ' كمية
        .Cells(row, 6).Value = 6.5  ' سعر
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-001"
        .Cells(row, 9).Value = "شحنة زيتون طازج من الموسم الجديد"
        row = row + 1
        
        ' شراء لبنة طبيعية
        .Cells(row, 1).Value = "PUR002"
        .Cells(row, 2).Value = currentDate + 1
        .Cells(row, 3).Value = "SUP002"
        .Cells(row, 4).Value = "RM002"
        .Cells(row, 5).Value = 80
        .Cells(row, 6).Value = 9.0
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-002"
        .Cells(row, 9).Value = "لبنة طازجة من مزارع إربد"
        row = row + 1
        
        ' شراء جوز مقشر
        .Cells(row, 1).Value = "PUR003"
        .Cells(row, 2).Value = currentDate + 2
        .Cells(row, 3).Value = "SUP003"
        .Cells(row, 4).Value = "RM003"
        .Cells(row, 5).Value = 25
        .Cells(row, 6).Value = 18.0
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-003"
        .Cells(row, 9).Value = "جوز عجلوني درجة أولى"
        row = row + 1
        
        ' شراء عين الجمل
        .Cells(row, 1).Value = "PUR004"
        .Cells(row, 2).Value = currentDate + 3
        .Cells(row, 3).Value = "SUP003"
        .Cells(row, 4).Value = "RM004"
        .Cells(row, 5).Value = 20
        .Cells(row, 6).Value = 22.0
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-004"
        .Cells(row, 9).Value = "عين جمل فاخر مستورد"
        row = row + 1
        
        ' شراء دقيق
        .Cells(row, 1).Value = "PUR005"
        .Cells(row, 2).Value = currentDate + 4
        .Cells(row, 3).Value = "SUP004"
        .Cells(row, 4).Value = "RM005"
        .Cells(row, 5).Value = 500
        .Cells(row, 6).Value = 0.85
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-005"
        .Cells(row, 9).Value = "دقيق صفر من مطاحن الأردن"
        row = row + 1
        
        ' مشتريات الأسبوع الثاني
        currentDate = currentDate + 7
        
        ' شراء سمن وسكر
        .Cells(row, 1).Value = "PUR006"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "SUP005"
        .Cells(row, 4).Value = "RM006"
        .Cells(row, 5).Value = 50
        .Cells(row, 6).Value = 3.2
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-006"
        .Cells(row, 9).Value = "سمن نباتي صحي"
        row = row + 1
        
        .Cells(row, 1).Value = "PUR007"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "SUP005"
        .Cells(row, 4).Value = "RM007"
        .Cells(row, 5).Value = 200
        .Cells(row, 6).Value = 1.1
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-007"
        .Cells(row, 9).Value = "سكر أبيض ناعم"
        row = row + 1
        
        ' مشتريات إضافية للأسبوع الثالث والرابع
        currentDate = currentDate + 7
        
        ' تجديد مخزون الزيتون
        .Cells(row, 1).Value = "PUR008"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "SUP001"
        .Cells(row, 4).Value = "RM001"
        .Cells(row, 5).Value = 200
        .Cells(row, 6).Value = 6.3  ' سعر أفضل للكمية الكبيرة
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-008"
        .Cells(row, 9).Value = "شحنة زيتون إضافية - خصم كمية"
        row = row + 1
        
        ' تجديد مخزون اللبنة
        .Cells(row, 1).Value = "PUR009"
        .Cells(row, 2).Value = currentDate + 2
        .Cells(row, 3).Value = "SUP002"
        .Cells(row, 4).Value = "RM002"
        .Cells(row, 5).Value = 120
        .Cells(row, 6).Value = 8.8  ' سعر أفضل
        .Cells(row, 7).Formula = "=E" & row & "*F" & row
        .Cells(row, 8).Value = "INV-2024-009"
        .Cells(row, 9).Value = "لبنة طازجة - دفعة ثانية"
        row = row + 1
    End With
End Sub

Sub تطبيق_دورة_انتاج_كاملة()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("اوامر_الانتاج")

    ws.Range("A4:K100").ClearContents

    Dim currentDate As Date
    currentDate = DateSerial(2024, 1, 8)  ' بداية الإنتاج بعد أسبوع من المشتريات

    Dim row As Long
    row = 4

    With ws
        ' أمر إنتاج الزيتون الأخضر المشوي باللبنة
        .Cells(row, 1).Value = "PRO001"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "FG001"
        .Cells(row, 4).Value = "زيتون أخضر مشوي باللبنة"
        .Cells(row, 5).Value = 200  ' كمية الإنتاج
        .Cells(row, 6).Value = 0    ' سيتم حسابها بالماكرو
        .Cells(row, 7).Value = 120  ' تكلفة العمالة (3 عمال × 8 ساعات × 5 دنانير)
        .Cells(row, 8).Value = 80   ' تكاليف إضافية (كهرباء، غاز، تعبئة)
        .Cells(row, 9).Formula = "=F" & row & "+G" & row & "+H" & row
        .Cells(row, 10).Value = "مكتمل"
        .Cells(row, 11).Value = "دفعة تجريبية للسوق"
        row = row + 1

        ' أمر إنتاج المعمول بالجوز
        .Cells(row, 1).Value = "PRO002"
        .Cells(row, 2).Value = currentDate + 1
        .Cells(row, 3).Value = "FG002"
        .Cells(row, 4).Value = "معمول بالجوز"
        .Cells(row, 5).Value = 150
        .Cells(row, 6).Value = 0
        .Cells(row, 7).Value = 90   ' تكلفة العمالة
        .Cells(row, 8).Value = 60   ' تكاليف إضافية
        .Cells(row, 9).Formula = "=F" & row & "+G" & row & "+H" & row
        .Cells(row, 10).Value = "مكتمل"
        .Cells(row, 11).Value = "إنتاج للموسم"
        row = row + 1

        ' أمر إنتاج المعمول بعين الجمل
        .Cells(row, 1).Value = "PRO003"
        .Cells(row, 2).Value = currentDate + 2
        .Cells(row, 3).Value = "FG003"
        .Cells(row, 4).Value = "معمول بعين الجمل"
        .Cells(row, 5).Value = 100
        .Cells(row, 6).Value = 0
        .Cells(row, 7).Value = 75   ' تكلفة العمالة
        .Cells(row, 8).Value = 50   ' تكاليف إضافية
        .Cells(row, 9).Formula = "=F" & row & "+G" & row & "+H" & row
        .Cells(row, 10).Value = "مكتمل"
        .Cells(row, 11).Value = "منتج فاخر للمناسبات"
        row = row + 1

        ' أمر إنتاج إضافي للزيتون (الأسبوع الثالث)
        .Cells(row, 1).Value = "PRO004"
        .Cells(row, 2).Value = currentDate + 14
        .Cells(row, 3).Value = "FG001"
        .Cells(row, 4).Value = "زيتون أخضر مشوي باللبنة"
        .Cells(row, 5).Value = 300
        .Cells(row, 6).Value = 0
        .Cells(row, 7).Value = 180  ' تكلفة عمالة أكبر
        .Cells(row, 8).Value = 120  ' تكاليف إضافية أكبر
        .Cells(row, 9).Formula = "=F" & row & "+G" & row & "+H" & row
        .Cells(row, 10).Value = "قيد التنفيذ"
        .Cells(row, 11).Value = "دفعة كبيرة لتلبية الطلب"
        row = row + 1

        ' أمر إنتاج إضافي للمعمول بالجوز
        .Cells(row, 1).Value = "PRO005"
        .Cells(row, 2).Value = currentDate + 16
        .Cells(row, 3).Value = "FG002"
        .Cells(row, 4).Value = "معمول بالجوز"
        .Cells(row, 5).Value = 200
        .Cells(row, 6).Value = 0
        .Cells(row, 7).Value = 120
        .Cells(row, 8).Value = 80
        .Cells(row, 9).Formula = "=F" & row & "+G" & row & "+H" & row
        .Cells(row, 10).Value = "مجدول"
        .Cells(row, 11).Value = "إنتاج للأسبوع القادم"
        row = row + 1
    End With
End Sub

Sub تطبيق_مبيعات_شهر_كامل()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المبيعات")

    ws.Range("A4:J100").ClearContents

    Dim currentDate As Date
    currentDate = DateSerial(2024, 1, 10)  ' بداية المبيعات بعد يومين من الإنتاج

    Dim row As Long
    row = 4

    With ws
        ' مبيعات الأسبوع الأول
        ' بيع للسوبر ماركت
        .Cells(row, 1).Value = "SAL001"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "CUS001"
        .Cells(row, 4).Value = "FG001"
        .Cells(row, 5).Value = "زيتون أخضر مشوي باللبنة"
        .Cells(row, 6).Value = 50
        .Cells(row, 7).Value = 14.5
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "نقداً"
        .Cells(row, 10).Value = "طلب تجريبي من السوبر ماركت"
        row = row + 1

        ' بيع للمخبز
        .Cells(row, 1).Value = "SAL002"
        .Cells(row, 2).Value = currentDate + 1
        .Cells(row, 3).Value = "CUS002"
        .Cells(row, 4).Value = "FG002"
        .Cells(row, 5).Value = "معمول بالجوز"
        .Cells(row, 6).Value = 30
        .Cells(row, 7).Value = 9.0  ' سعر جملة
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "آجل 30 يوم"
        .Cells(row, 10).Value = "طلب جملة للمخبز"
        row = row + 1

        ' بيع للمطعم
        .Cells(row, 1).Value = "SAL003"
        .Cells(row, 2).Value = currentDate + 2
        .Cells(row, 3).Value = "CUS003"
        .Cells(row, 4).Value = "FG001"
        .Cells(row, 5).Value = "زيتون أخضر مشوي باللبنة"
        .Cells(row, 6).Value = 25
        .Cells(row, 7).Value = 13.5  ' سعر خاص للمطاعم
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "نقداً"
        .Cells(row, 10).Value = "طلب خاص للمطعم"
        row = row + 1

        ' بيع للجمعية الخيرية
        .Cells(row, 1).Value = "SAL004"
        .Cells(row, 2).Value = currentDate + 3
        .Cells(row, 3).Value = "CUS004"
        .Cells(row, 4).Value = "FG003"
        .Cells(row, 5).Value = "معمول بعين الجمل"
        .Cells(row, 6).Value = 20
        .Cells(row, 7).Value = 9.5   ' سعر خيري
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "نقداً"
        .Cells(row, 10).Value = "تبرع جزئي للجمعية"
        row = row + 1

        ' مبيعات الأسبوع الثاني
        currentDate = currentDate + 7

        ' طلب كبير من السوبر ماركت
        .Cells(row, 1).Value = "SAL005"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "CUS001"
        .Cells(row, 4).Value = "FG001"
        .Cells(row, 5).Value = "زيتون أخضر مشوي باللبنة"
        .Cells(row, 6).Value = 80
        .Cells(row, 7).Value = 14.0  ' خصم للكمية
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "آجل 15 يوم"
        .Cells(row, 10).Value = "طلب كبير - خصم كمية"
        row = row + 1

        ' بيع لكافتيريا الجامعة
        .Cells(row, 1).Value = "SAL006"
        .Cells(row, 2).Value = currentDate + 1
        .Cells(row, 3).Value = "CUS005"
        .Cells(row, 4).Value = "FG002"
        .Cells(row, 5).Value = "معمول بالجوز"
        .Cells(row, 6).Value = 40
        .Cells(row, 7).Value = 8.5   ' سعر مؤسسات
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "آجل 30 يوم"
        .Cells(row, 10).Value = "طلب للكافتيريا"
        row = row + 1

        ' مبيعات متنوعة للأسبوع الثالث
        currentDate = currentDate + 7

        ' طلب مختلط من المطعم
        .Cells(row, 1).Value = "SAL007"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "CUS003"
        .Cells(row, 4).Value = "FG002"
        .Cells(row, 5).Value = "معمول بالجوز"
        .Cells(row, 6).Value = 35
        .Cells(row, 7).Value = 9.0
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "نقداً"
        .Cells(row, 10).Value = "طلب إضافي للمطعم"
        row = row + 1

        .Cells(row, 1).Value = "SAL008"
        .Cells(row, 2).Value = currentDate
        .Cells(row, 3).Value = "CUS003"
        .Cells(row, 4).Value = "FG003"
        .Cells(row, 5).Value = "معمول بعين الجمل"
        .Cells(row, 6).Value = 15
        .Cells(row, 7).Value = 10.0
        .Cells(row, 8).Formula = "=F" & row & "*G" & row
        .Cells(row, 9).Value = "نقداً"
        .Cells(row, 10).Value = "طلب فاخر للمطعم"
        row = row + 1
    End With
End Sub

Sub انشاء_تقارير_شاملة()
    ' إنشاء تقارير شاملة للمثال العملي
    Call تحديث_مخزون_المواد_الخام
    Call حساب_تكلفة_الانتاج
    Call تحديث_مخزون_المنتجات_التامة
    Call تحديث_مخزون_بعد_المبيعات
    Call انشاء_تقرير_سريع

    ' إنشاء تقرير خاص للمثال العملي
    Call انشاء_تقرير_المثال_العملي
End Sub

Sub انشاء_تقرير_المثال_العملي()
    Dim wsReport As Worksheet
    Set wsReport = ThisWorkbook.Worksheets.Add
    wsReport.Name = "تقرير_المثال_العملي_" & Format(Date, "dd_mm_yyyy")

    With wsReport
        ' العنوان الرئيسي
        .Range("A1").Value = "تقرير المثال العملي - شركة أبو فرح للمواد الغذائية"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        .Range("A1").Interior.Color = RGB(54, 96, 146)
        .Range("A1").Font.Color = RGB(255, 255, 255)

        ' معلومات التقرير
        .Range("A3").Value = "تاريخ التقرير:"
        .Range("B3").Value = Date
        .Range("A4").Value = "فترة التقرير:"
        .Range("B4").Value = "يناير 2024"

        ' ملخص المبيعات
        .Range("A6").Value = "ملخص المبيعات:"
        .Range("A6").Font.Bold = True
        .Range("A6").Interior.Color = RGB(255, 255, 0)

        .Range("A7").Value = "المنتج"
        .Range("B7").Value = "الكمية المباعة"
        .Range("C7").Value = "إجمالي المبيعات"
        .Range("D7").Value = "متوسط السعر"
        .Range("A7:D7").Font.Bold = True
        .Range("A7:D7").Interior.Color = RGB(217, 217, 217)

        .Range("A8").Value = "زيتون أخضر مشوي باللبنة"
        .Range("B8").Value = 155  ' 50+25+80
        .Range("C8").Value = 2162.5  ' حساب تقريبي
        .Range("D8").Value = 13.95

        .Range("A9").Value = "معمول بالجوز"
        .Range("B9").Value = 105  ' 30+40+35
        .Range("C9").Value = 945  ' حساب تقريبي
        .Range("D9").Value = 9.0

        .Range("A10").Value = "معمول بعين الجمل"
        .Range("B10").Value = 35  ' 20+15
        .Range("C10").Value = 340  ' حساب تقريبي
        .Range("D10").Value = 9.71

        ' إجمالي المبيعات
        .Range("A11").Value = "الإجمالي"
        .Range("B11").Formula = "=SUM(B8:B10)"
        .Range("C11").Formula = "=SUM(C8:C10)"
        .Range("D11").Formula = "=C11/B11"
        .Range("A11:D11").Font.Bold = True
        .Range("A11:D11").Interior.Color = RGB(255, 215, 0)

        ' ملخص التكاليف
        .Range("A13").Value = "ملخص التكاليف:"
        .Range("A13").Font.Bold = True
        .Range("A13").Interior.Color = RGB(255, 255, 0)

        .Range("A14").Value = "نوع التكلفة"
        .Range("B14").Value = "المبلغ (دينار)"
        .Range("C14").Value = "النسبة %"
        .Range("A14:C14").Font.Bold = True
        .Range("A14:C14").Interior.Color = RGB(217, 217, 217)

        .Range("A15").Value = "تكلفة المواد الخام"
        .Range("B15").Value = 2500  ' تقدير
        .Range("C15").Value = "70%"

        .Range("A16").Value = "تكلفة العمالة"
        .Range("B16").Value = 585   ' 120+90+75+180+120
        .Range("C16").Value = "16%"

        .Range("A17").Value = "التكاليف الإضافية"
        .Range("B17").Value = 390   ' 80+60+50+120+80
        .Range("C17").Value = "11%"

        .Range("A18").Value = "تكاليف أخرى"
        .Range("B18").Value = 100   ' تقدير
        .Range("C18").Value = "3%"

        .Range("A19").Value = "إجمالي التكاليف"
        .Range("B19").Formula = "=SUM(B15:B18)"
        .Range("C19").Value = "100%"
        .Range("A19:C19").Font.Bold = True
        .Range("A19:C19").Interior.Color = RGB(255, 215, 0)

        ' تحليل الربحية
        .Range("A21").Value = "تحليل الربحية:"
        .Range("A21").Font.Bold = True
        .Range("A21").Interior.Color = RGB(255, 255, 0)

        .Range("A22").Value = "إجمالي المبيعات"
        .Range("B22").Formula = "=C11"

        .Range("A23").Value = "إجمالي التكاليف"
        .Range("B23").Formula = "=B19"

        .Range("A24").Value = "صافي الربح"
        .Range("B24").Formula = "=B22-B23"
        .Range("A24:B24").Font.Bold = True
        .Range("A24:B24").Interior.Color = RGB(144, 238, 144)

        .Range("A25").Value = "هامش الربح %"
        .Range("B25").Formula = "=B24/B22*100"
        .Range("A25:B25").Font.Bold = True

        ' توصيات
        .Range("A27").Value = "التوصيات:"
        .Range("A27").Font.Bold = True
        .Range("A27").Interior.Color = RGB(255, 255, 0)

        .Range("A28").Value = "• زيادة إنتاج الزيتون المشوي باللبنة (الأكثر ربحية)"
        .Range("A29").Value = "• تحسين تكلفة المعمول بعين الجمل"
        .Range("A30").Value = "• البحث عن موردين بأسعار أفضل للمكسرات"
        .Range("A31").Value = "• زيادة الطلبات الجملة لتحسين الهوامش"
        .Range("A32").Value = "• تطوير منتجات جديدة بناءً على نجاح الزيتون"

        .Columns("A:F").AutoFit
    End With

    MsgBox "تم إنشاء تقرير المثال العملي بنجاح!", vbInformation
End Sub

Sub فحص_الشيتات_غير_المكتملة()
    Dim wsReport As Worksheet
    Set wsReport = ThisWorkbook.Worksheets.Add
    wsReport.Name = "فحص_الشيتات_" & Format(Date, "dd_mm_yyyy")

    With wsReport
        .Range("A1").Value = "تقرير فحص الشيتات غير المكتملة"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:D1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        .Range("A1").Interior.Color = RGB(255, 0, 0)
        .Range("A1").Font.Color = RGB(255, 255, 255)

        .Range("A3").Value = "اسم الشيت"
        .Range("B3").Value = "الحالة"
        .Range("C3").Value = "عدد الصفوف"
        .Range("D3").Value = "الملاحظات"
        .Range("A3:D3").Font.Bold = True
        .Range("A3:D3").Interior.Color = RGB(217, 217, 217)

        Dim row As Long
        row = 4

        ' فحص كل شيت
        Dim ws As Worksheet
        For Each ws In ThisWorkbook.Worksheets
            .Cells(row, 1).Value = ws.Name

            Dim lastRow As Long
            lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).row
            .Cells(row, 3).Value = lastRow - 3  ' طرح صفوف العناوين

            ' تحديد الحالة
            If lastRow <= 3 Then
                .Cells(row, 2).Value = "فارغ"
                .Cells(row, 2).Interior.Color = RGB(255, 0, 0)
                .Cells(row, 4).Value = "لا يحتوي على بيانات"
            ElseIf lastRow <= 10 Then
                .Cells(row, 2).Value = "قليل البيانات"
                .Cells(row, 2).Interior.Color = RGB(255, 165, 0)
                .Cells(row, 4).Value = "يحتاج المزيد من البيانات"
            Else
                .Cells(row, 2).Value = "مكتمل"
                .Cells(row, 2).Interior.Color = RGB(144, 238, 144)
                .Cells(row, 4).Value = "يحتوي على بيانات كافية"
            End If

            row = row + 1
        Next ws

        .Columns("A:D").AutoFit
    End With

    MsgBox "تم إنشاء تقرير فحص الشيتات!", vbInformation
End Sub
