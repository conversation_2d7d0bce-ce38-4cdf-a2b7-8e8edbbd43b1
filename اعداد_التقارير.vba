Sub اعداد_تقرير_المخزون(ws As Worksheet)
    ' إعداد تقرير المخزون الشامل
    With ws
        .Range("A1").Value = "تقرير المخزون الشامل"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:G1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' قسم مخزون المواد الخام
        .Range("A3").Value = "مخزون المواد الخام"
        .Range("A3").Font.Bold = True
        .Range("A3").Interior.Color = RGB(255, 255, 0)
        .Range("A3:G3").Merge
        
        .Range("A4").Value = "كود المادة"
        .Range("B4").Value = "اسم المادة"
        .Range("C4").Value = "الكمية"
        .Range("D4").Value = "متوسط التكلفة"
        .Range("E4").Value = "قيمة المخزون"
        .Range("F4").Value = "الحد الأدنى"
        .Range("G4").Value = "الحالة"
        
        ' تنسيق العناوين
        .Range("A4:G4").Font.Bold = True
        .Range("A4:G4").Interior.Color = RGB(217, 217, 217)
        .Range("A4:G4").Borders.LineStyle = xlContinuous
        
        ' بيانات مخزون المواد الخام
        .Range("A5").Value = "RM001"
        .Range("B5").Value = "زيتون أخضر"
        .Range("C5").Value = 150
        .Range("D5").Value = 5.5
        .Range("E5").Formula = "=C5*D5"
        .Range("F5").Value = 100
        .Range("G5").Formula = "=IF(C5<F5,""منخفض"",""متوفر"")"
        
        .Range("A6").Value = "RM002"
        .Range("B6").Value = "لبنة طبيعية"
        .Range("C6").Value = 75
        .Range("D6").Value = 8.0
        .Range("E6").Formula = "=C6*D6"
        .Range("F6").Value = 50
        .Range("G6").Formula = "=IF(C6<F6,""منخفض"",""متوفر"")"
        
        .Range("A7").Value = "RM003"
        .Range("B7").Value = "جوز مقشر"
        .Range("C7").Value = 30
        .Range("D7").Value = 15.0
        .Range("E7").Formula = "=C7*D7"
        .Range("F7").Value = 25
        .Range("G7").Formula = "=IF(C7<F7,""منخفض"",""متوفر"")"
        
        ' إجمالي مخزون المواد الخام
        .Range("A8").Value = "إجمالي قيمة المواد الخام:"
        .Range("A8").Font.Bold = True
        .Range("E8").Formula = "=SUM(E5:E7)"
        .Range("E8").Font.Bold = True
        
        ' قسم مخزون المنتجات التامة
        .Range("A10").Value = "مخزون المنتجات التامة"
        .Range("A10").Font.Bold = True
        .Range("A10").Interior.Color = RGB(255, 255, 0)
        .Range("A10:G10").Merge
        
        .Range("A11").Value = "كود المنتج"
        .Range("B11").Value = "اسم المنتج"
        .Range("C11").Value = "الكمية"
        .Range("D11").Value = "تكلفة الوحدة"
        .Range("E11").Value = "قيمة المخزون"
        .Range("F11").Value = "سعر البيع"
        .Range("G11").Value = "الربح المتوقع"
        
        ' تنسيق العناوين
        .Range("A11:G11").Font.Bold = True
        .Range("A11:G11").Interior.Color = RGB(217, 217, 217)
        .Range("A11:G11").Borders.LineStyle = xlContinuous
        
        ' بيانات مخزون المنتجات التامة
        .Range("A12").Value = "FG001"
        .Range("B12").Value = "زيتون أخضر مشوي باللبنة"
        .Range("C12").Value = 80
        .Range("D12").Value = 3.5
        .Range("E12").Formula = "=C12*D12"
        .Range("F12").Value = 12.0
        .Range("G12").Formula = "=C12*(F12-D12)"
        
        .Range("A13").Value = "FG002"
        .Range("B13").Value = "معمول بالجوز"
        .Range("C13").Value = 40
        .Range("D13").Value = 5.35
        .Range("E13").Formula = "=C13*D13"
        .Range("F13").Value = 8.5
        .Range("G13").Formula = "=C13*(F13-D13)"
        
        .Range("A14").Value = "FG003"
        .Range("B14").Value = "معمول بعين الجمل"
        .Range("C14").Value = 25
        .Range("D14").Value = 5.8
        .Range("E14").Formula = "=C14*D14"
        .Range("F14").Value = 9.0
        .Range("G14").Formula = "=C14*(F14-D14)"
        
        ' إجمالي مخزون المنتجات التامة
        .Range("A15").Value = "إجمالي قيمة المنتجات التامة:"
        .Range("A15").Font.Bold = True
        .Range("E15").Formula = "=SUM(E12:E14)"
        .Range("E15").Font.Bold = True
        .Range("G15").Formula = "=SUM(G12:G14)"
        .Range("G15").Font.Bold = True
        
        ' إجمالي عام
        .Range("A17").Value = "إجمالي قيمة المخزون:"
        .Range("A17").Font.Bold = True
        .Range("A17").Font.Size = 12
        .Range("E17").Formula = "=E8+E15"
        .Range("E17").Font.Bold = True
        .Range("E17").Font.Size = 12
        
        ' تنسيق الجدول
        .Range("A4:G17").Borders.LineStyle = xlContinuous
        .Columns("A:G").AutoFit
    End With
End Sub

Sub اعداد_التقارير_المالية(ws As Worksheet)
    ' إعداد التقارير المالية
    With ws
        .Range("A1").Value = "التقارير المالية والمحاسبية"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' تقرير الأرباح والخسائر
        .Range("A3").Value = "قائمة الأرباح والخسائر"
        .Range("A3").Font.Bold = True
        .Range("A3").Interior.Color = RGB(255, 255, 0)
        .Range("A3:C3").Merge
        
        .Range("A4").Value = "البيان"
        .Range("B4").Value = "المبلغ"
        .Range("C4").Value = "النسبة %"
        
        ' تنسيق العناوين
        .Range("A4:C4").Font.Bold = True
        .Range("A4:C4").Interior.Color = RGB(217, 217, 217)
        .Range("A4:C4").Borders.LineStyle = xlContinuous
        
        ' الإيرادات
        .Range("A5").Value = "إجمالي المبيعات"
        .Range("B5").Value = 1000  ' قيمة تجريبية
        .Range("C5").Formula = "=B5/B5*100"
        
        ' التكاليف
        .Range("A6").Value = "تكلفة المواد الخام"
        .Range("B6").Value = 400   ' قيمة تجريبية
        .Range("C6").Formula = "=B6/B5*100"
        
        .Range("A7").Value = "تكلفة العمالة المباشرة"
        .Range("B7").Value = 150   ' قيمة تجريبية
        .Range("C7").Formula = "=B7/B5*100"
        
        .Range("A8").Value = "التكاليف الصناعية غير المباشرة"
        .Range("B8").Value = 100   ' قيمة تجريبية
        .Range("C8").Formula = "=B8/B5*100"
        
        .Range("A9").Value = "إجمالي تكلفة الإنتاج"
        .Range("B9").Formula = "=B6+B7+B8"
        .Range("C9").Formula = "=B9/B5*100"
        .Range("A9:C9").Font.Bold = True
        
        ' الربح الإجمالي
        .Range("A10").Value = "الربح الإجمالي"
        .Range("B10").Formula = "=B5-B9"
        .Range("C10").Formula = "=B10/B5*100"
        .Range("A10:C10").Font.Bold = True
        .Range("A10:C10").Interior.Color = RGB(144, 238, 144)
        
        ' المصروفات التشغيلية
        .Range("A11").Value = "المصروفات الإدارية"
        .Range("B11").Value = 80    ' قيمة تجريبية
        .Range("C11").Formula = "=B11/B5*100"
        
        .Range("A12").Value = "مصروفات البيع والتسويق"
        .Range("B12").Value = 50    ' قيمة تجريبية
        .Range("C12").Formula = "=B12/B5*100"
        
        .Range("A13").Value = "إجمالي المصروفات التشغيلية"
        .Range("B13").Formula = "=B11+B12"
        .Range("C13").Formula = "=B13/B5*100"
        .Range("A13:C13").Font.Bold = True
        
        ' صافي الربح
        .Range("A14").Value = "صافي الربح"
        .Range("B14").Formula = "=B10-B13"
        .Range("C14").Formula = "=B14/B5*100"
        .Range("A14:C14").Font.Bold = True
        .Range("A14:C14").Interior.Color = RGB(255, 215, 0)
        
        ' تحليل الربحية بالمنتج
        .Range("A16").Value = "تحليل الربحية بالمنتج"
        .Range("A16").Font.Bold = True
        .Range("A16").Interior.Color = RGB(255, 255, 0)
        .Range("A16:E16").Merge
        
        .Range("A17").Value = "المنتج"
        .Range("B17").Value = "الكمية المباعة"
        .Range("C17").Value = "إجمالي المبيعات"
        .Range("D17").Value = "إجمالي التكلفة"
        .Range("E17").Value = "صافي الربح"
        
        ' تنسيق العناوين
        .Range("A17:E17").Font.Bold = True
        .Range("A17:E17").Interior.Color = RGB(217, 217, 217)
        .Range("A17:E17").Borders.LineStyle = xlContinuous
        
        ' بيانات الربحية
        .Range("A18").Value = "زيتون أخضر مشوي باللبنة"
        .Range("B18").Value = 20
        .Range("C18").Value = 240  ' 20 * 12
        .Range("D18").Value = 70   ' 20 * 3.5
        .Range("E18").Formula = "=C18-D18"
        
        .Range("A19").Value = "معمول بالجوز"
        .Range("B19").Value = 15
        .Range("C19").Value = 127.5  ' 15 * 8.5
        .Range("D19").Value = 80.25  ' 15 * 5.35
        .Range("E19").Formula = "=C19-D19"
        
        .Range("A20").Value = "معمول بعين الجمل"
        .Range("B20").Value = 10
        .Range("C20").Value = 90   ' 10 * 9
        .Range("D20").Value = 58   ' 10 * 5.8
        .Range("E20").Formula = "=C20-D20"
        
        ' إجماليات
        .Range("A21").Value = "الإجمالي"
        .Range("B21").Formula = "=SUM(B18:B20)"
        .Range("C21").Formula = "=SUM(C18:C20)"
        .Range("D21").Formula = "=SUM(D18:D20)"
        .Range("E21").Formula = "=SUM(E18:E20)"
        .Range("A21:E21").Font.Bold = True
        
        ' تنسيق الجداول
        .Range("A4:C14").Borders.LineStyle = xlContinuous
        .Range("A17:E21").Borders.LineStyle = xlContinuous
        .Columns("A:E").AutoFit
    End With
End Sub

Sub تشغيل_النظام_الكامل()
    ' تشغيل جميع إجراءات إعداد النظام
    Call انشاء_نظام_محاسبة_التكاليف
    
    MsgBox "تم إنشاء نظام محاسبة التكاليف المتكامل بنجاح!" & vbCrLf & _
           "يتضمن النظام:" & vbCrLf & _
           "• إدارة المشتريات والمخزون" & vbCrLf & _
           "• إدارة الإنتاج والوصفات" & vbCrLf & _
           "• إدارة المبيعات" & vbCrLf & _
           "• التقارير المالية والمحاسبية", _
           vbInformation, "نظام محاسبة التكاليف"
End Sub
