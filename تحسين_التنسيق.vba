' ماكرو تحسين التنسيق والواجهة
' يطبق تنسيق احترافي على جميع أوراق النظام

Sub تطبيق_التنسيق_الاحترافي()
    Application.ScreenUpdating = False
    
    Dim wb As Workbook
    Dim ws As Worksheet
    
    Set wb = ThisWorkbook
    
    ' تطبيق التنسيق على كل ورقة
    For Each ws In wb.Worksheets
        Call تنسيق_ورقة_عامة(ws)
        
        ' تنسيق خاص لكل ورقة
        Select Case ws.Name
            Case "لوحة_التحكم"
                Call تنسيق_لوحة_التحكم(ws)
            Case "الموردين", "العملاء"
                Call تنسيق_جداول_البيانات_الاساسية(ws)
            Case "المواد_الخام", "المنتجات_التامة"
                Call تنسيق_جداول_المواد_والمنتجات(ws)
            Case "المشتريات", "المبيعات"
                Call تنسيق_جداول_العمليات(ws)
            Case "مخزون_المواد_الخام", "مخزون_المنتجات_التامة"
                Call تنسيق_جداول_المخزون(ws)
            Case "الوصفات", "اوامر_الانتاج"
                Call تنسيق_جداول_الانتاج(ws)
            Case "تقرير_التكاليف", "تقرير_المخزون", "التقارير_المالية"
                Call تنسيق_التقارير(ws)
        End Select
    Next ws
    
    Application.ScreenUpdating = True
    MsgBox "تم تطبيق التنسيق الاحترافي على جميع الأوراق!", vbInformation
End Sub

Sub تنسيق_ورقة_عامة(ws As Worksheet)
    With ws
        ' تنسيق الخط العام
        .Cells.Font.Name = "Calibri"
        .Cells.Font.Size = 11
        
        ' تنسيق الاتجاه للنص العربي
        .Cells.HorizontalAlignment = xlRight
        
        ' تجميد الأسطر العلوية
        .Range("A4").Select
        ActiveWindow.FreezePanes = True
        
        ' إخفاء خطوط الشبكة
        ActiveWindow.DisplayGridlines = False
        
        ' تطبيق تنسيق الحدود
        .UsedRange.Borders.LineStyle = xlContinuous
        .UsedRange.Borders.Weight = xlThin
        .UsedRange.Borders.Color = RGB(200, 200, 200)
        
        ' تطبيق تنسيق متناوب للصفوف
        Call تطبيق_تنسيق_متناوب(ws)
        
        ' ضبط عرض الأعمدة تلقائياً
        .Columns.AutoFit
    End With
End Sub

Sub تنسيق_لوحة_التحكم(ws As Worksheet)
    With ws
        ' تنسيق العنوان الرئيسي
        .Range("A1").Interior.Color = RGB(54, 96, 146)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        .Range("A1").Font.Size = 20
        .Range("A1").Font.Bold = True
        
        ' تنسيق العنوان الفرعي
        .Range("A3").Interior.Color = RGB(79, 129, 189)
        .Range("A3").Font.Color = RGB(255, 255, 255)
        .Range("A3").Font.Size = 16
        
        ' تنسيق أزرار التنقل
        .Range("B6:B10").Interior.Color = RGB(173, 216, 230)
        .Range("B6:B10").Font.Bold = True
        .Range("B6:B10").Font.Size = 12
        .Range("B6:B10").Borders.LineStyle = xlContinuous
        .Range("B6:B10").Borders.Weight = xlMedium
        .Range("B6:B10").Borders.Color = RGB(54, 96, 146)
        
        ' تنسيق قائمة المنتجات
        .Range("A13:A16").Interior.Color = RGB(255, 255, 204)
        .Range("A13:A16").Font.Color = RGB(54, 96, 146)
        .Range("A13:A16").Font.Bold = True
        
        ' إضافة شعار أو صورة (اختياري)
        .Range("E6:F10").Interior.Color = RGB(240, 240, 240)
        .Range("E6").Value = "شعار الشركة"
        .Range("E6").HorizontalAlignment = xlCenter
        .Range("E6").VerticalAlignment = xlCenter
    End With
End Sub

Sub تنسيق_جداول_البيانات_الاساسية(ws As Worksheet)
    With ws
        ' تنسيق العنوان
        .Range("A1").Interior.Color = RGB(54, 96, 146)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        
        ' تنسيق عناوين الأعمدة
        Dim headerRange As Range
        Set headerRange = .Range("A3").CurrentRegion.Rows(3)
        headerRange.Interior.Color = RGB(217, 217, 217)
        headerRange.Font.Bold = True
        headerRange.Font.Size = 12
        headerRange.Borders.LineStyle = xlContinuous
        headerRange.Borders.Weight = xlMedium
        
        ' تنسيق البيانات
        Dim dataRange As Range
        Set dataRange = .Range("A4").CurrentRegion
        dataRange.Borders.LineStyle = xlContinuous
        dataRange.Borders.Weight = xlThin
        dataRange.Borders.Color = RGB(200, 200, 200)
    End With
End Sub

Sub تنسيق_جداول_المواد_والمنتجات(ws As Worksheet)
    With ws
        ' تنسيق مشابه للجداول الأساسية مع ألوان مختلفة
        .Range("A1").Interior.Color = RGB(79, 129, 189)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        
        ' تنسيق خاص للأسعار والكميات
        Dim priceColumns As Range
        If ws.Name = "المواد_الخام" Then
            Set priceColumns = .Range("D:D,E:E") ' أعمدة السعر والحد الأدنى
        Else
            Set priceColumns = .Range("D:D,E:E") ' أعمدة سعر البيع ومدة الصلاحية
        End If
        
        priceColumns.NumberFormat = "#,##0.00"
        priceColumns.HorizontalAlignment = xlCenter
    End With
End Sub

Sub تنسيق_جداول_العمليات(ws As Worksheet)
    With ws
        ' تنسيق العنوان بلون مميز
        .Range("A1").Interior.Color = RGB(146, 208, 80)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        
        ' تنسيق أعمدة التواريخ
        Dim dateColumns As Range
        Set dateColumns = .Range("B:B") ' عمود التاريخ
        dateColumns.NumberFormat = "dd/mm/yyyy"
        dateColumns.HorizontalAlignment = xlCenter
        
        ' تنسيق أعمدة المبالغ
        Dim amountColumns As Range
        If ws.Name = "المشتريات" Then
            Set amountColumns = .Range("F:G") ' أعمدة السعر والإجمالي
        Else ' المبيعات
            Set amountColumns = .Range("G:H") ' أعمدة السعر والإجمالي
        End If
        
        amountColumns.NumberFormat = "#,##0.00"
        amountColumns.HorizontalAlignment = xlCenter
        
        ' تمييز الصفوف حسب الحالة
        Call تمييز_صفوف_حسب_الحالة(ws)
    End With
End Sub

Sub تنسيق_جداول_المخزون(ws As Worksheet)
    With ws
        ' تنسيق العنوان
        .Range("A1").Interior.Color = RGB(192, 80, 77)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        
        ' تنسيق أعمدة الكميات والقيم
        Dim quantityColumns As Range
        Set quantityColumns = .Range("C:C,E:E") ' أعمدة الكمية وقيمة المخزون
        quantityColumns.NumberFormat = "#,##0.00"
        quantityColumns.HorizontalAlignment = xlCenter
        
        ' تنسيق عمود الحالة
        Dim statusColumn As Range
        If ws.Name = "مخزون_المواد_الخام" Then
            Set statusColumn = .Range("G:G")
        Else
            Set statusColumn = .Range("H:H")
        End If
        
        ' تطبيق تنسيق شرطي للحالة
        Call تطبيق_تنسيق_شرطي_للحالة(ws, statusColumn)
    End With
End Sub

Sub تنسيق_جداول_الانتاج(ws As Worksheet)
    With ws
        ' تنسيق العنوان
        .Range("A1").Interior.Color = RGB(149, 79, 114)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        
        If ws.Name = "اوامر_الانتاج" Then
            ' تنسيق أعمدة التكاليف
            Dim costColumns As Range
            Set costColumns = .Range("F:I") ' أعمدة التكاليف
            costColumns.NumberFormat = "#,##0.00"
            costColumns.HorizontalAlignment = xlCenter
            
            ' تنسيق عمود الحالة
            Call تطبيق_تنسيق_شرطي_للحالة(ws, .Range("J:J"))
        End If
    End With
End Sub

Sub تنسيق_التقارير(ws As Worksheet)
    With ws
        ' تنسيق العنوان
        .Range("A1").Interior.Color = RGB(54, 96, 146)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        .Range("A1").Font.Size = 18
        
        ' تنسيق العناوين الفرعية
        Dim subHeaders As Range
        Set subHeaders = .Range("A3,A10,A16") ' العناوين الفرعية
        subHeaders.Interior.Color = RGB(255, 255, 0)
        subHeaders.Font.Bold = True
        subHeaders.Font.Size = 14
        
        ' تنسيق أعمدة الأرقام
        .UsedRange.NumberFormat = "#,##0.00"
        
        ' تمييز الإجماليات
        Call تمييز_صفوف_الاجماليات(ws)
    End With
End Sub

Sub تطبيق_تنسيق_متناوب(ws As Worksheet)
    Dim lastRow As Long
    Dim i As Long
    
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    For i = 4 To lastRow Step 2
        ws.Rows(i).Interior.Color = RGB(248, 248, 248)
    Next i
End Sub

Sub تطبيق_تنسيق_شرطي_للحالة(ws As Worksheet, statusRange As Range)
    Dim cell As Range
    
    For Each cell In statusRange.Cells
        If cell.Value = "متوفر" Then
            cell.Interior.Color = RGB(144, 238, 144) ' أخضر فاتح
        ElseIf cell.Value = "منخفض" Then
            cell.Interior.Color = RGB(255, 165, 0) ' برتقالي
        ElseIf cell.Value = "مكتمل" Then
            cell.Interior.Color = RGB(144, 238, 144) ' أخضر فاتح
        ElseIf cell.Value = "قيد التنفيذ" Then
            cell.Interior.Color = RGB(255, 255, 0) ' أصفر
        End If
    Next cell
End Sub

Sub تمييز_صفوف_حسب_الحالة(ws As Worksheet)
    ' تطبيق تنسيق خاص للصفوف حسب حالة العملية
    Dim lastRow As Long
    Dim i As Long
    
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    For i = 4 To lastRow
        If ws.Cells(i, 1).Value <> "" Then
            ' تمييز الصفوف الجديدة
            If ws.Cells(i, 2).Value = Date Then
                ws.Rows(i).Interior.Color = RGB(255, 255, 204) ' أصفر فاتح
            End If
        End If
    Next i
End Sub

Sub تمييز_صفوف_الاجماليات(ws As Worksheet)
    Dim cell As Range
    
    For Each cell In ws.UsedRange.Columns(1).Cells
        If InStr(cell.Value, "إجمالي") > 0 Or InStr(cell.Value, "صافي") > 0 Then
            cell.EntireRow.Interior.Color = RGB(255, 215, 0) ' ذهبي
            cell.EntireRow.Font.Bold = True
        End If
    Next cell
End Sub

Sub اضافة_شريط_ادوات_مخصص()
    ' إضافة شريط أدوات مخصص للنظام
    Dim customToolbar As CommandBar
    
    On Error Resume Next
    Application.CommandBars("نظام التكاليف").Delete
    On Error GoTo 0
    
    Set customToolbar = Application.CommandBars.Add("نظام التكاليف", msoBarTop, False, True)
    
    With customToolbar
        .Visible = True
        
        ' إضافة أزرار للعمليات الأساسية
        With .Controls.Add(msoControlButton)
            .Caption = "تحديث شامل"
            .OnAction = "تحديث_شامل_للنظام"
            .FaceId = 159
        End With
        
        With .Controls.Add(msoControlButton)
            .Caption = "تقرير سريع"
            .OnAction = "انشاء_تقرير_سريع"
            .FaceId = 590
        End With
        
        With .Controls.Add(msoControlButton)
            .Caption = "تطبيق التنسيق"
            .OnAction = "تطبيق_التنسيق_الاحترافي"
            .FaceId = 267
        End With
    End With
End Sub
