# تعليمات تشغيل المثال العملي الواقعي

## 🎯 نظرة عامة

هذا المثال العملي يحاكي **شركة أبو فرح للمواد الغذائية** ويتضمن:
- 5 موردين حقيقيين من الأردن
- 5 عملاء متنوعين (تجزئة، جملة، مطاعم، مؤسسات)
- 7 مواد خام بأسعار واقعية
- 3 منتجات تامة مع وصفات تفصيلية
- دورة كاملة من المشتريات والإنتاج والمبيعات لشهر يناير 2024

## 🚀 خطوات التشغيل السريع

### الخطوة 1: إنشاء النظام الأساسي
```bash
cscript انشاء_نظام_بسيط.vbs
```
**النتيجة**: إنشاء ملف Excel مع 16 ورقة عمل

### الخطوة 2: فتح Excel وتفعيل الماكرو
1. افتح `نظام_محاسبة_التكاليف_متكامل.xlsx`
2. اختر **Enable Macros** عند السؤال
3. انتقل إلى **Developer Tab** → **Visual Basic**

### الخطوة 3: استيراد ملف المثال العملي
1. في محرر VBA، اختر **File** → **Import File**
2. اختر ملف `مثال_عملي_واقعي.vba`
3. اضغط **F5** لتشغيل `تطبيق_مثال_عملي_واقعي()`

### الخطوة 4: مراجعة النتائج
بعد التشغيل ستجد:
- بيانات كاملة في جميع الأوراق
- ورقة جديدة: `تقرير_المثال_العملي`
- ورقة جديدة: `فحص_الشيتات`

## 📊 ما سيتم إدخاله

### البيانات الأساسية

#### الموردين (5 شركات)
| الكود | اسم المورد | المدينة | التخصص |
|-------|------------|---------|----------|
| SUP001 | شركة الزيتون الذهبي | عمان | زيتون ولبنة |
| SUP002 | مزارع الأردن للألبان | إربد | منتجات الألبان |
| SUP003 | شركة المكسرات الطبيعية | عجلون | جوز وعين جمل |
| SUP004 | مطاحن الأردن الكبرى | الزرقاء | دقيق |
| SUP005 | شركة الغذاء المتكامل | عمان | سمن وسكر |

#### العملاء (5 أنواع)
| الكود | اسم العميل | النوع | التقييم |
|-------|------------|-------|----------|
| CUS001 | سوبر ماركت الأمل | تجزئة | ممتاز |
| CUS002 | مخابز الفردوس | جملة | جيد |
| CUS003 | مطعم الأصالة | مطاعم | ممتاز |
| CUS004 | جمعية الرحمة | جمعيات | ممتاز |
| CUS005 | كافتيريا الجامعة | مؤسسات | جيد |

#### المواد الخام (7 مواد)
| الكود | المادة | السعر (د/كغ) | المورد |
|-------|--------|-------------|--------|
| RM001 | زيتون أخضر طازج | 6.5 | SUP001 |
| RM002 | لبنة طبيعية | 9.0 | SUP002 |
| RM003 | جوز مقشر | 18.0 | SUP003 |
| RM004 | عين الجمل | 22.0 | SUP003 |
| RM005 | دقيق أبيض | 0.85 | SUP004 |
| RM006 | سمن نباتي | 3.2 | SUP005 |
| RM007 | سكر ناعم | 1.1 | SUP005 |

### العمليات الشهرية

#### المشتريات (9 عمليات)
- **الأسبوع الأول**: مشتريات أساسية لجميع المواد
- **الأسبوع الثاني**: تجديد السمن والسكر
- **الأسبوع الثالث**: تجديد الزيتون واللبنة

**إجمالي قيمة المشتريات**: ~4,500 دينار

#### الإنتاج (5 أوامر)
| الأمر | المنتج | الكمية | الحالة |
|-------|--------|---------|--------|
| PRO001 | زيتون مشوي | 200 | مكتمل |
| PRO002 | معمول جوز | 150 | مكتمل |
| PRO003 | معمول عين جمل | 100 | مكتمل |
| PRO004 | زيتون مشوي | 300 | قيد التنفيذ |
| PRO005 | معمول جوز | 200 | مجدول |

#### المبيعات (8 عمليات)
- **للسوبر ماركت**: 130 وحدة زيتون
- **للمخبز**: 70 وحدة معمول
- **للمطعم**: 75 وحدة متنوعة
- **للجمعية**: 20 وحدة خيرية

**إجمالي المبيعات**: 295 وحدة بقيمة 3,450 دينار

## 📈 التحليل المتوقع

### الربحية بالمنتج
1. **زيتون أخضر مشوي باللبنة**
   - تكلفة المواد: 4.53 د
   - سعر البيع: 14.5 د
   - هامش الربح: 69%

2. **معمول بالجوز**
   - تكلفة المواد: 1.87 د
   - سعر البيع: 9.5 د
   - هامش الربح: 80%

3. **معمول بعين الجمل**
   - تكلفة المواد: 2.23 د
   - سعر البيع: 10.5 د
   - هامش الربح: 79%

### الأداء المالي المتوقع
- **إجمالي المبيعات**: 3,450 د
- **إجمالي التكاليف**: 2,450 د
- **صافي الربح**: 1,000 د
- **هامش الربح**: 29%

## 🔍 ما يمكن مراجعته بعد التشغيل

### 1. الأوراق الأساسية
- **الموردين**: 5 موردين مع تفاصيل كاملة
- **العملاء**: 5 عملاء متنوعين
- **المواد_الخام**: 7 مواد بأسعار واقعية
- **المنتجات_التامة**: 3 منتجات مع أسعار

### 2. الوصفات
- **الوصفات**: وصفات تفصيلية للمنتجات الثلاثة
- حساب دقيق للكميات والتكاليف

### 3. العمليات
- **المشتريات**: 9 عمليات شراء متنوعة
- **اوامر_الانتاج**: 5 أوامر بحالات مختلفة
- **المبيعات**: 8 عمليات بيع لعملاء مختلفين

### 4. المخزون
- **مخزون_المواد_الخام**: أرصدة محدثة
- **مخزون_المنتجات_التامة**: أرصدة بعد الإنتاج والمبيعات

### 5. التقارير
- **تقرير_التكاليف**: تكاليف مفصلة لكل منتج
- **تقرير_المخزون**: حالة المخزون الحالية
- **التقارير_المالية**: ملخص مالي شامل

## 🧪 اختبار الوظائف

### اختبار الماكرو
```vba
' تشغيل هذه الماكرو بالترتيب
Call تحديث_مخزون_المواد_الخام()
Call حساب_تكلفة_الانتاج()
Call تحديث_مخزون_المنتجات_التامة()
Call تحديث_مخزون_بعد_المبيعات()
Call انشاء_تقرير_سريع()
```

### اختبار التقارير
- راجع **تقرير_المثال_العملي** للتحليل الشامل
- راجع **فحص_الشيتات** لحالة البيانات

## 🎯 التوصيات بعد المراجعة

### 1. تحليل الأرباح
- راجع هوامش الربح لكل منتج
- حدد المنتجات الأكثر ربحية
- خطط لزيادة إنتاج المنتجات المربحة

### 2. تحسين التكاليف
- ابحث عن موردين بأسعار أفضل
- حسّن كفاءة الإنتاج
- قلل التكاليف الإضافية

### 3. تطوير المبيعات
- ركز على العملاء الأكثر ربحية
- طور منتجات جديدة
- حسّن استراتيجية التسعير

### 4. إدارة المخزون
- حدد الحد الأمثل للمخزون
- طور نظام تنبيهات
- حسّن دورة المخزون

## 🔧 استكشاف الأخطاء

### إذا لم يعمل الماكرو
1. تأكد من تفعيل الماكرو في Excel
2. تحقق من استيراد الملف بشكل صحيح
3. تأكد من وجود جميع الأوراق المطلوبة

### إذا ظهرت أخطاء في الحسابات
1. تحقق من صحة البيانات المدخلة
2. راجع الصيغ في الخلايا
3. تأكد من تطابق أكواد المواد والمنتجات

### إذا كانت التقارير فارغة
1. تأكد من وجود بيانات في الأوراق الأساسية
2. شغل ماكرو التحديث الشامل
3. تحقق من صحة المراجع في الصيغ

## 📞 الدعم

للحصول على المساعدة:
1. راجع **دليل_المستخدم_نظام_محاسبة_التكاليف.md**
2. راجع **تعليمات_التشغيل_النهائية.md**
3. راجع **تحليل_النظام_الشامل.md**

---

**نصيحة**: ابدأ بمراجعة ورقة "لوحة_التحكم" ثم انتقل تدريجياً عبر الأوراق الأخرى لفهم تدفق البيانات والعمليات.
