-- نظام محاسبة التكاليف لمصنع المواد الغذائية
-- تصميم قاعدة البيانات

-- ملاحظة: هذا ملف نصي يحتوي على تصميم قاعدة البيانات
-- سيتم إنشاء قاعدة البيانات الفعلية باستخدام Microsoft Access

-- ========================================
-- الجداول الأساسية (Master Tables)
-- ========================================

-- جدول الوحدات
CREATE TABLE الوحدات (
    كود_الوحدة AUTOINCREMENT PRIMARY KEY,
    اسم_الوحدة TEXT(50) NOT NULL,
    اختصار_الوحدة TEXT(10),
    ملاحظات MEMO
);

-- جدول الموردين
CREATE TABLE الموردين (
    كود_المورد AUTOINCREMENT PRIMARY KEY,
    اسم_المورد TEXT(100) NOT NULL,
    العنوان TEXT(200),
    الهاتف TEXT(50),
    الجوال TEXT(50),
    البريد_الالكتروني TEXT(100),
    الرقم_الضريبي TEXT(50),
    حالة_المورد TEXT(20) DEFAULT 'نشط',
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- جدول العملاء
CREATE TABLE العملاء (
    كود_العميل AUTOINCREMENT PRIMARY KEY,
    اسم_العميل TEXT(100) NOT NULL,
    العنوان TEXT(200),
    الهاتف TEXT(50),
    الجوال TEXT(50),
    البريد_الالكتروني TEXT(100),
    الرقم_الضريبي TEXT(50),
    حالة_العميل TEXT(20) DEFAULT 'نشط',
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- جدول المواد الخام
CREATE TABLE المواد_الخام (
    كود_المادة AUTOINCREMENT PRIMARY KEY,
    اسم_المادة TEXT(100) NOT NULL,
    كود_الوحدة INTEGER REFERENCES الوحدات(كود_الوحدة),
    الحد_الادنى DOUBLE DEFAULT 0,
    الحد_الاقصى DOUBLE DEFAULT 0,
    متوسط_التكلفة CURRENCY DEFAULT 0,
    حالة_المادة TEXT(20) DEFAULT 'نشط',
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- جدول المنتجات التامة
CREATE TABLE المنتجات_التامة (
    كود_المنتج AUTOINCREMENT PRIMARY KEY,
    اسم_المنتج TEXT(100) NOT NULL,
    كود_الوحدة INTEGER REFERENCES الوحدات(كود_الوحدة),
    سعر_البيع CURRENCY DEFAULT 0,
    تكلفة_الانتاج CURRENCY DEFAULT 0,
    الحد_الادنى DOUBLE DEFAULT 0,
    الحد_الاقصى DOUBLE DEFAULT 0,
    حالة_المنتج TEXT(20) DEFAULT 'نشط',
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- جدول المخازن
CREATE TABLE المخازن (
    كود_المخزن AUTOINCREMENT PRIMARY KEY,
    اسم_المخزن TEXT(100) NOT NULL,
    نوع_المخزن TEXT(50), -- مواد خام، منتجات تامة
    العنوان TEXT(200),
    المسؤول TEXT(100),
    حالة_المخزن TEXT(20) DEFAULT 'نشط',
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- ========================================
-- جداول المخزون والحركات
-- ========================================

-- جدول أرصدة المواد الخام
CREATE TABLE ارصدة_المواد_الخام (
    كود_الرصيد AUTOINCREMENT PRIMARY KEY,
    كود_المادة INTEGER REFERENCES المواد_الخام(كود_المادة),
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن),
    الكمية_المتاحة DOUBLE DEFAULT 0,
    متوسط_التكلفة CURRENCY DEFAULT 0,
    القيمة_الاجمالية CURRENCY DEFAULT 0,
    تاريخ_اخر_تحديث DATETIME DEFAULT Now()
);

-- جدول أرصدة المنتجات التامة
CREATE TABLE ارصدة_المنتجات_التامة (
    كود_الرصيد AUTOINCREMENT PRIMARY KEY,
    كود_المنتج INTEGER REFERENCES المنتجات_التامة(كود_المنتج),
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن),
    الكمية_المتاحة DOUBLE DEFAULT 0,
    متوسط_التكلفة CURRENCY DEFAULT 0,
    القيمة_الاجمالية CURRENCY DEFAULT 0,
    تاريخ_اخر_تحديث DATETIME DEFAULT Now()
);

-- جدول حركات المخزون
CREATE TABLE حركات_المخزون (
    كود_الحركة AUTOINCREMENT PRIMARY KEY,
    نوع_الحركة TEXT(50), -- وارد، صادر
    نوع_المادة TEXT(50), -- مادة خام، منتج تام
    كود_المادة INTEGER,
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن),
    الكمية DOUBLE,
    سعر_الوحدة CURRENCY,
    القيمة_الاجمالية CURRENCY,
    رقم_المستند TEXT(50),
    نوع_المستند TEXT(50), -- فاتورة شراء، امر انتاج، فاتورة بيع
    تاريخ_الحركة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- ========================================
-- جداول الوصفات والإنتاج
-- ========================================

-- جدول وصفات المنتجات
CREATE TABLE وصفات_المنتجات (
    كود_الوصفة AUTOINCREMENT PRIMARY KEY,
    كود_المنتج INTEGER REFERENCES المنتجات_التامة(كود_المنتج),
    اسم_الوصفة TEXT(100),
    كمية_الانتاج DOUBLE DEFAULT 1,
    تاريخ_الاضافة DATETIME DEFAULT Now(),
    حالة_الوصفة TEXT(20) DEFAULT 'نشط',
    ملاحظات MEMO
);

-- جدول تفاصيل الوصفات
CREATE TABLE تفاصيل_الوصفات (
    كود_التفصيل AUTOINCREMENT PRIMARY KEY,
    كود_الوصفة INTEGER REFERENCES وصفات_المنتجات(كود_الوصفة),
    كود_المادة INTEGER REFERENCES المواد_الخام(كود_المادة),
    الكمية_المطلوبة DOUBLE,
    ملاحظات MEMO
);

-- جدول أوامر الإنتاج
CREATE TABLE اوامر_الانتاج (
    رقم_امر_الانتاج AUTOINCREMENT PRIMARY KEY,
    كود_المنتج INTEGER REFERENCES المنتجات_التامة(كود_المنتج),
    كود_الوصفة INTEGER REFERENCES وصفات_المنتجات(كود_الوصفة),
    كمية_الانتاج DOUBLE,
    تاريخ_الامر DATETIME DEFAULT Now(),
    تاريخ_البدء DATETIME,
    تاريخ_الانتهاء DATETIME,
    حالة_الامر TEXT(50) DEFAULT 'جديد', -- جديد، قيد التنفيذ، مكتمل، ملغي
    تكلفة_المواد_الخام CURRENCY DEFAULT 0,
    تكلفة_العمالة CURRENCY DEFAULT 0,
    التكاليف_المباشرة CURRENCY DEFAULT 0,
    التكاليف_غير_المباشرة CURRENCY DEFAULT 0,
    اجمالي_التكلفة CURRENCY DEFAULT 0,
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن),
    ملاحظات MEMO
);

-- جدول تفاصيل استهلاك المواد الخام في الإنتاج
CREATE TABLE تفاصيل_استهلاك_الانتاج (
    كود_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_امر_الانتاج INTEGER REFERENCES اوامر_الانتاج(رقم_امر_الانتاج),
    كود_المادة INTEGER REFERENCES المواد_الخام(كود_المادة),
    الكمية_المستهلكة DOUBLE,
    سعر_الوحدة CURRENCY,
    القيمة_الاجمالية CURRENCY
);

-- ========================================
-- جداول المشتريات
-- ========================================

-- جدول فواتير المشتريات
CREATE TABLE فواتير_المشتريات (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    كود_المورد INTEGER REFERENCES الموردين(كود_المورد),
    تاريخ_الفاتورة DATETIME DEFAULT Now(),
    رقم_فاتورة_المورد TEXT(50),
    اجمالي_الفاتورة CURRENCY DEFAULT 0,
    الخصم CURRENCY DEFAULT 0,
    الضريبة CURRENCY DEFAULT 0,
    صافي_الفاتورة CURRENCY DEFAULT 0,
    حالة_الفاتورة TEXT(50) DEFAULT 'مؤكدة',
    ملاحظات MEMO
);

-- جدول تفاصيل فواتير المشتريات
CREATE TABLE تفاصيل_فواتير_المشتريات (
    كود_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الفاتورة INTEGER REFERENCES فواتير_المشتريات(رقم_الفاتورة),
    كود_المادة INTEGER REFERENCES المواد_الخام(كود_المادة),
    الكمية DOUBLE,
    سعر_الوحدة CURRENCY,
    القيمة_الاجمالية CURRENCY,
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن)
);

-- ========================================
-- جداول المبيعات
-- ========================================

-- جدول فواتير المبيعات
CREATE TABLE فواتير_المبيعات (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    كود_العميل INTEGER REFERENCES العملاء(كود_العميل),
    تاريخ_الفاتورة DATETIME DEFAULT Now(),
    اجمالي_الفاتورة CURRENCY DEFAULT 0,
    الخصم CURRENCY DEFAULT 0,
    الضريبة CURRENCY DEFAULT 0,
    صافي_الفاتورة CURRENCY DEFAULT 0,
    حالة_الفاتورة TEXT(50) DEFAULT 'مؤكدة',
    ملاحظات MEMO
);

-- جدول تفاصيل فواتير المبيعات
CREATE TABLE تفاصيل_فواتير_المبيعات (
    كود_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الفاتورة INTEGER REFERENCES فواتير_المبيعات(رقم_الفاتورة),
    كود_المنتج INTEGER REFERENCES المنتجات_التامة(كود_المنتج),
    الكمية DOUBLE,
    سعر_الوحدة CURRENCY,
    القيمة_الاجمالية CURRENCY,
    تكلفة_الوحدة CURRENCY,
    اجمالي_التكلفة CURRENCY,
    كود_المخزن INTEGER REFERENCES المخازن(كود_المخزن)
);

-- ========================================
-- جداول التكاليف الإضافية
-- ========================================

-- جدول أنواع التكاليف
CREATE TABLE انواع_التكاليف (
    كود_نوع_التكلفة AUTOINCREMENT PRIMARY KEY,
    اسم_نوع_التكلفة TEXT(100) NOT NULL,
    تصنيف_التكلفة TEXT(50), -- مباشرة، غير مباشرة، عمالة
    حالة_النوع TEXT(20) DEFAULT 'نشط'
);

-- جدول تكاليف أوامر الإنتاج
CREATE TABLE تكاليف_اوامر_الانتاج (
    كود_التكلفة AUTOINCREMENT PRIMARY KEY,
    رقم_امر_الانتاج INTEGER REFERENCES اوامر_الانتاج(رقم_امر_الانتاج),
    كود_نوع_التكلفة INTEGER REFERENCES انواع_التكاليف(كود_نوع_التكلفة),
    قيمة_التكلفة CURRENCY,
    تاريخ_التكلفة DATETIME DEFAULT Now(),
    ملاحظات MEMO
);
