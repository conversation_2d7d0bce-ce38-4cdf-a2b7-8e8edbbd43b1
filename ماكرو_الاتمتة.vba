' ماكرو أتمتة نظام محاسبة التكاليف
' يحتوي على إجراءات لأتمتة العمليات الأساسية

Option Explicit

' إجراء تحديث مخزون المواد الخام بعد المشتريات
Sub تحديث_مخزون_المواد_الخام()
    Dim wsPurchases As Worksheet
    Dim wsInventory As Worksheet
    Dim lastRowPur As Long, lastRowInv As Long
    Dim i As Long, j As Long
    Dim materialCode As String
    Dim quantity As Double, unitPrice As Double
    Dim found As Boolean
    
    Set wsPurchases = ThisWorkbook.Worksheets("المشتريات")
    Set wsInventory = ThisWorkbook.Worksheets("مخزون_المواد_الخام")
    
    lastRowPur = wsPurchases.Cells(wsPurchases.Rows.Count, "A").End(xlUp).Row
    lastRowInv = wsInventory.Cells(wsInventory.Rows.Count, "A").End(xlUp).Row
    
    ' تحديث المخزون لكل عملية شراء
    For i = 4 To lastRowPur ' البدء من الصف 4 (بعد العناوين)
        materialCode = wsPurchases.Cells(i, 4).Value ' كود المادة
        quantity = wsPurchases.Cells(i, 5).Value ' الكمية
        unitPrice = wsPurchases.Cells(i, 6).Value ' سعر الوحدة
        
        If materialCode <> "" And quantity > 0 Then
            found = False
            
            ' البحث عن المادة في المخزون
            For j = 4 To lastRowInv
                If wsInventory.Cells(j, 1).Value = materialCode Then
                    ' تحديث الكمية ومتوسط التكلفة
                    Dim currentQty As Double, currentAvgCost As Double
                    Dim newQty As Double, newAvgCost As Double
                    
                    currentQty = wsInventory.Cells(j, 3).Value
                    currentAvgCost = wsInventory.Cells(j, 4).Value
                    
                    newQty = currentQty + quantity
                    newAvgCost = ((currentQty * currentAvgCost) + (quantity * unitPrice)) / newQty
                    
                    wsInventory.Cells(j, 3).Value = newQty
                    wsInventory.Cells(j, 4).Value = Round(newAvgCost, 2)
                    wsInventory.Cells(j, 5).Value = Round(newQty * newAvgCost, 2) ' قيمة المخزون
                    wsInventory.Cells(j, 6).Value = Date ' آخر تحديث
                    
                    ' تحديث حالة المخزون
                    Dim minLevel As Double
                    minLevel = Application.VLookup(materialCode, ThisWorkbook.Worksheets("المواد_الخام").Range("A:E"), 5, False)
                    If newQty < minLevel Then
                        wsInventory.Cells(j, 7).Value = "منخفض"
                    Else
                        wsInventory.Cells(j, 7).Value = "متوفر"
                    End If
                    
                    found = True
                    Exit For
                End If
            Next j
            
            ' إذا لم توجد المادة، أضفها كسطر جديد
            If Not found Then
                lastRowInv = lastRowInv + 1
                wsInventory.Cells(lastRowInv, 1).Value = materialCode
                wsInventory.Cells(lastRowInv, 2).Value = Application.VLookup(materialCode, ThisWorkbook.Worksheets("المواد_الخام").Range("A:B"), 2, False)
                wsInventory.Cells(lastRowInv, 3).Value = quantity
                wsInventory.Cells(lastRowInv, 4).Value = unitPrice
                wsInventory.Cells(lastRowInv, 5).Value = quantity * unitPrice
                wsInventory.Cells(lastRowInv, 6).Value = Date
                wsInventory.Cells(lastRowInv, 7).Value = "متوفر"
            End If
        End If
    Next i
    
    MsgBox "تم تحديث مخزون المواد الخام بنجاح!", vbInformation
End Sub

' إجراء حساب تكلفة الإنتاج حسب الوصفة
Sub حساب_تكلفة_الانتاج()
    Dim wsProduction As Worksheet
    Dim wsRecipes As Worksheet
    Dim wsInventory As Worksheet
    Dim lastRowProd As Long, lastRowRec As Long
    Dim i As Long, j As Long
    Dim productCode As String, materialCode As String
    Dim requiredQty As Double, materialCost As Double
    Dim totalMaterialCost As Double
    
    Set wsProduction = ThisWorkbook.Worksheets("اوامر_الانتاج")
    Set wsRecipes = ThisWorkbook.Worksheets("الوصفات")
    Set wsInventory = ThisWorkbook.Worksheets("مخزون_المواد_الخام")
    
    lastRowProd = wsProduction.Cells(wsProduction.Rows.Count, "A").End(xlUp).Row
    lastRowRec = wsRecipes.Cells(wsRecipes.Rows.Count, "A").End(xlUp).Row
    
    ' حساب تكلفة المواد الخام لكل أمر إنتاج
    For i = 4 To lastRowProd
        productCode = wsProduction.Cells(i, 3).Value ' كود المنتج
        Dim productionQty As Double
        productionQty = wsProduction.Cells(i, 5).Value ' كمية الإنتاج
        
        If productCode <> "" And productionQty > 0 Then
            totalMaterialCost = 0
            
            ' البحث عن مكونات المنتج في الوصفات
            For j = 4 To lastRowRec
                If wsRecipes.Cells(j, 1).Value = productCode Then
                    materialCode = wsRecipes.Cells(j, 2).Value
                    requiredQty = wsRecipes.Cells(j, 4).Value * productionQty
                    
                    ' الحصول على تكلفة المادة من المخزون
                    materialCost = Application.VLookup(materialCode, wsInventory.Range("A:D"), 4, False)
                    If Not IsError(materialCost) Then
                        totalMaterialCost = totalMaterialCost + (requiredQty * materialCost)
                    End If
                End If
            Next j
            
            ' تحديث تكلفة المواد الخام في أمر الإنتاج
            wsProduction.Cells(i, 6).Value = Round(totalMaterialCost, 2)
        End If
    Next i
    
    MsgBox "تم حساب تكلفة المواد الخام لأوامر الإنتاج!", vbInformation
End Sub

' إجراء تحديث مخزون المنتجات التامة بعد الإنتاج
Sub تحديث_مخزون_المنتجات_التامة()
    Dim wsProduction As Worksheet
    Dim wsFinishedGoods As Worksheet
    Dim lastRowProd As Long, lastRowFG As Long
    Dim i As Long, j As Long
    Dim productCode As String
    Dim productionQty As Double, unitCost As Double
    Dim found As Boolean
    
    Set wsProduction = ThisWorkbook.Worksheets("اوامر_الانتاج")
    Set wsFinishedGoods = ThisWorkbook.Worksheets("مخزون_المنتجات_التامة")
    
    lastRowProd = wsProduction.Cells(wsProduction.Rows.Count, "A").End(xlUp).Row
    lastRowFG = wsFinishedGoods.Cells(wsFinishedGoods.Rows.Count, "A").End(xlUp).Row
    
    ' تحديث المخزون للأوامر المكتملة فقط
    For i = 4 To lastRowProd
        If wsProduction.Cells(i, 10).Value = "مكتمل" Then ' الحالة
            productCode = wsProduction.Cells(i, 3).Value
            productionQty = wsProduction.Cells(i, 5).Value
            
            ' حساب تكلفة الوحدة
            Dim totalCost As Double
            totalCost = wsProduction.Cells(i, 6).Value + wsProduction.Cells(i, 7).Value + wsProduction.Cells(i, 8).Value
            unitCost = totalCost / productionQty
            
            found = False
            
            ' البحث عن المنتج في المخزون
            For j = 4 To lastRowFG
                If wsFinishedGoods.Cells(j, 1).Value = productCode Then
                    ' تحديث الكمية ومتوسط التكلفة
                    Dim currentQty As Double, currentAvgCost As Double
                    Dim newQty As Double, newAvgCost As Double
                    
                    currentQty = wsFinishedGoods.Cells(j, 3).Value
                    currentAvgCost = wsFinishedGoods.Cells(j, 4).Value
                    
                    newQty = currentQty + productionQty
                    newAvgCost = ((currentQty * currentAvgCost) + (productionQty * unitCost)) / newQty
                    
                    wsFinishedGoods.Cells(j, 3).Value = newQty
                    wsFinishedGoods.Cells(j, 4).Value = Round(newAvgCost, 2)
                    wsFinishedGoods.Cells(j, 5).Value = Round(newQty * newAvgCost, 2)
                    wsFinishedGoods.Cells(j, 7).Value = Date
                    
                    ' تحديث حالة المخزون
                    If newQty < 10 Then ' حد أدنى افتراضي
                        wsFinishedGoods.Cells(j, 8).Value = "منخفض"
                    Else
                        wsFinishedGoods.Cells(j, 8).Value = "متوفر"
                    End If
                    
                    found = True
                    Exit For
                End If
            Next j
            
            ' إذا لم يوجد المنتج، أضفه كسطر جديد
            If Not found Then
                lastRowFG = lastRowFG + 1
                wsFinishedGoods.Cells(lastRowFG, 1).Value = productCode
                wsFinishedGoods.Cells(lastRowFG, 2).Value = Application.VLookup(productCode, ThisWorkbook.Worksheets("المنتجات_التامة").Range("A:B"), 2, False)
                wsFinishedGoods.Cells(lastRowFG, 3).Value = productionQty
                wsFinishedGoods.Cells(lastRowFG, 4).Value = Round(unitCost, 2)
                wsFinishedGoods.Cells(lastRowFG, 5).Value = Round(productionQty * unitCost, 2)
                wsFinishedGoods.Cells(lastRowFG, 6).Value = Application.VLookup(productCode, ThisWorkbook.Worksheets("المنتجات_التامة").Range("A:D"), 4, False)
                wsFinishedGoods.Cells(lastRowFG, 7).Value = Date
                wsFinishedGoods.Cells(lastRowFG, 8).Value = "متوفر"
            End If
        End If
    Next i
    
    MsgBox "تم تحديث مخزون المنتجات التامة بنجاح!", vbInformation
End Sub

' إجراء تحديث المخزون بعد المبيعات
Sub تحديث_مخزون_بعد_المبيعات()
    Dim wsSales As Worksheet
    Dim wsFinishedGoods As Worksheet
    Dim lastRowSales As Long, lastRowFG As Long
    Dim i As Long, j As Long
    Dim productCode As String
    Dim soldQty As Double
    
    Set wsSales = ThisWorkbook.Worksheets("المبيعات")
    Set wsFinishedGoods = ThisWorkbook.Worksheets("مخزون_المنتجات_التامة")
    
    lastRowSales = wsSales.Cells(wsSales.Rows.Count, "A").End(xlUp).Row
    lastRowFG = wsFinishedGoods.Cells(wsFinishedGoods.Rows.Count, "A").End(xlUp).Row
    
    ' تحديث المخزون لكل عملية بيع
    For i = 4 To lastRowSales
        productCode = wsSales.Cells(i, 4).Value ' كود المنتج
        soldQty = wsSales.Cells(i, 6).Value ' الكمية المباعة
        
        If productCode <> "" And soldQty > 0 Then
            ' البحث عن المنتج في المخزون وتقليل الكمية
            For j = 4 To lastRowFG
                If wsFinishedGoods.Cells(j, 1).Value = productCode Then
                    Dim currentQty As Double
                    currentQty = wsFinishedGoods.Cells(j, 3).Value
                    
                    If currentQty >= soldQty Then
                        wsFinishedGoods.Cells(j, 3).Value = currentQty - soldQty
                        ' إعادة حساب قيمة المخزون
                        wsFinishedGoods.Cells(j, 5).Value = wsFinishedGoods.Cells(j, 3).Value * wsFinishedGoods.Cells(j, 4).Value
                        wsFinishedGoods.Cells(j, 7).Value = Date
                        
                        ' تحديث حالة المخزون
                        If wsFinishedGoods.Cells(j, 3).Value < 10 Then
                            wsFinishedGoods.Cells(j, 8).Value = "منخفض"
                        Else
                            wsFinishedGoods.Cells(j, 8).Value = "متوفر"
                        End If
                    Else
                        MsgBox "تحذير: الكمية المطلوبة (" & soldQty & ") أكبر من المتوفر (" & currentQty & ") للمنتج " & productCode, vbExclamation
                    End If
                    Exit For
                End If
            Next j
        End If
    Next i
    
    MsgBox "تم تحديث مخزون المنتجات بعد المبيعات!", vbInformation
End Sub

' إجراء شامل لتحديث جميع البيانات
Sub تحديث_شامل_للنظام()
    Application.ScreenUpdating = False
    
    Call تحديث_مخزون_المواد_الخام
    Call حساب_تكلفة_الانتاج
    Call تحديث_مخزون_المنتجات_التامة
    Call تحديث_مخزون_بعد_المبيعات
    
    Application.ScreenUpdating = True
    
    MsgBox "تم التحديث الشامل لجميع بيانات النظام بنجاح!", vbInformation, "تحديث شامل"
End Sub

' إجراء إنشاء تقرير سريع
Sub انشاء_تقرير_سريع()
    Dim wsReport As Worksheet
    Dim wsInventoryRM As Worksheet
    Dim wsInventoryFG As Worksheet
    
    ' إنشاء ورقة تقرير جديدة
    Set wsReport = ThisWorkbook.Worksheets.Add
    wsReport.Name = "تقرير_سريع_" & Format(Date, "dd_mm_yyyy")
    
    Set wsInventoryRM = ThisWorkbook.Worksheets("مخزون_المواد_الخام")
    Set wsInventoryFG = ThisWorkbook.Worksheets("مخزون_المنتجات_التامة")
    
    With wsReport
        .Range("A1").Value = "تقرير سريع - " & Date
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        
        .Range("A3").Value = "ملخص المخزون"
        .Range("A3").Font.Bold = True
        
        .Range("A4").Value = "إجمالي قيمة مخزون المواد الخام:"
        .Range("B4").Formula = "=SUM(مخزون_المواد_الخام.E:E)"
        
        .Range("A5").Value = "إجمالي قيمة مخزون المنتجات التامة:"
        .Range("B5").Formula = "=SUM(مخزون_المنتجات_التامة.E:E)"
        
        .Range("A6").Value = "إجمالي قيمة المخزون:"
        .Range("B6").Formula = "=B4+B5"
        .Range("A6:B6").Font.Bold = True
        
        .Columns("A:B").AutoFit
    End With
    
    MsgBox "تم إنشاء التقرير السريع في ورقة جديدة!", vbInformation
End Sub
