' إجراءات النظام والاختبار
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء النظام كاملاً
Public Sub انشاء_النظام_كاملا()
    On Error GoTo ErrorHandler
    
    MsgBox "سيتم الآن إنشاء نظام محاسبة التكاليف كاملاً..." & vbCrLf & _
           "هذه العملية قد تستغرق عدة دقائق.", vbInformation, "إنشاء النظام"
    
    ' إنشاء قاعدة البيانات والجداول
    Call انشاء_قاعدة_البيانات
    
    ' إنشاء النماذج الأساسية
    Call انشاء_النماذج_الاساسية
    
    ' إنشاء نماذج المشتريات والمخزون
    Call انشاء_نماذج_المشتريات_والمخزون
    
    ' إنشاء نماذج الإنتاج والوصفات
    Call انشاء_نماذج_الانتاج_والوصفات
    
    ' إنشاء نماذج المبيعات
    Call انشاء_نماذج_المبيعات
    
    ' إنشاء النموذج الرئيسي
    Call انشاء_النموذج_الرئيسي
    
    ' إدخال البيانات التجريبية
    Call ادخال_بيانات_تجريبية
    
    MsgBox "تم إنشاء النظام بنجاح!" & vbCrLf & _
           "يمكنك الآن البدء في استخدام النظام.", vbInformation, "اكتمل الإنشاء"
    
    ' فتح النموذج الرئيسي
    DoCmd.OpenForm "النموذج_الرئيسي"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النظام: " & Err.Description, vbCritical, "خطأ"
End Sub

' إجراء إدخال البيانات التجريبية
Public Sub ادخال_بيانات_تجريبية()
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Set db = CurrentDb()
    
    ' إدخال وحدات القياس
    db.Execute "INSERT INTO الوحدات (كود_الوحدة, اسم_الوحدة, نوع_الوحدة) VALUES " & _
               "('KG', 'كيلوجرام', 'وزن'), " & _
               "('GM', 'جرام', 'وزن'), " & _
               "('LT', 'لتر', 'حجم'), " & _
               "('PC', 'قطعة', 'عدد'), " & _
               "('PK', 'علبة', 'تعبئة')"
    
    ' إدخال أنواع التكاليف
    db.Execute "INSERT INTO انواع_التكاليف (كود_نوع_التكلفة, اسم_نوع_التكلفة, تصنيف_التكلفة, وصف_التكلفة) VALUES " & _
               "('LAB001', 'عمالة مباشرة', 'مباشرة', 'تكلفة العمالة المباشرة في الإنتاج'), " & _
               "('LAB002', 'عمالة غير مباشرة', 'غير مباشرة', 'تكلفة العمالة غير المباشرة'), " & _
               "('ELC001', 'كهرباء', 'غير مباشرة', 'تكلفة الكهرباء'), " & _
               "('GAS001', 'غاز', 'مباشرة', 'تكلفة الغاز للطبخ'), " & _
               "('PKG001', 'تعبئة وتغليف', 'مباشرة', 'تكلفة مواد التعبئة والتغليف')"
    
    ' إدخال المخازن
    db.Execute "INSERT INTO المخازن (كود_المخزن, اسم_المخزن, نوع_المخزن, موقع_المخزن, مسؤول_المخزن) VALUES " & _
               "('WH001', 'مخزن المواد الخام الرئيسي', 'مواد خام', 'الطابق الأرضي', 'أحمد محمد'), " & _
               "('WH002', 'مخزن المنتجات التامة', 'منتجات تامة', 'الطابق الأول', 'فاطمة علي'), " & _
               "('WH003', 'مخزن التبريد', 'مواد خام', 'قسم التبريد', 'محمد حسن')"
    
    ' إدخال موردين
    db.Execute "INSERT INTO الموردين (كود_المورد, اسم_المورد, عنوان_المورد, هاتف_المورد, ايميل_المورد, حالة_المورد) VALUES " & _
               "('SUP001', 'مزارع الزيتون المتحدة', 'القاهرة - مصر الجديدة', '01234567890', '<EMAIL>', 'نشط'), " & _
               "('SUP002', 'مصنع اللبنة الطبيعية', 'الإسكندرية - سموحة', '01098765432', '<EMAIL>', 'نشط'), " & _
               "('SUP003', 'تجارة المكسرات الذهبية', 'الجيزة - المهندسين', '01156789012', '<EMAIL>', 'نشط')"
    
    ' إدخال عملاء
    db.Execute "INSERT INTO العملاء (كود_العميل, اسم_العميل, عنوان_العميل, هاتف_العميل, ايميل_العميل, حالة_العميل) VALUES " & _
               "('CUS001', 'سوبر ماركت الأسرة', 'القاهرة - مدينة نصر', '0223456789', '<EMAIL>', 'نشط'), " & _
               "('CUS002', 'مطاعم الذوق الرفيع', 'الجيزة - الدقي', '0233445566', '<EMAIL>', 'نشط'), " & _
               "('CUS003', 'متجر الأطعمة الشرقية', 'الإسكندرية - ستانلي', '0344556677', '<EMAIL>', 'نشط')"
    
    ' إدخال المواد الخام
    db.Execute "INSERT INTO المواد_الخام (كود_المادة, اسم_المادة, كود_الوحدة, الحد_الادنى, الحد_الاقصى, حالة_المادة) VALUES " & _
               "('RM001', 'زيتون أخضر', 'KG', 50, 500, 'نشط'), " & _
               "('RM002', 'لبنة طبيعية', 'KG', 20, 200, 'نشط'), " & _
               "('RM003', 'جوز مقشر', 'KG', 10, 100, 'نشط'), " & _
               "('RM004', 'عين جمل مقشر', 'KG', 10, 100, 'نشط'), " & _
               "('RM005', 'دقيق أبيض', 'KG', 25, 250, 'نشط'), " & _
               "('RM006', 'سكر ناعم', 'KG', 15, 150, 'نشط'), " & _
               "('RM007', 'زيت زيتون', 'LT', 5, 50, 'نشط')"
    
    ' إدخال المنتجات التامة
    db.Execute "INSERT INTO المنتجات_التامة (كود_المنتج, اسم_المنتج, كود_الوحدة, سعر_البيع, الحد_الادنى, الحد_الاقصى, حالة_المنتج) VALUES " & _
               "('FP001', 'زيتون أخضر مشوي باللبنة', 'KG', 45.00, 10, 100, 'نشط'), " & _
               "('FP002', 'معمول بالجوز', 'KG', 60.00, 5, 50, 'نشط'), " & _
               "('FP003', 'معمول بعين الجمل', 'KG', 65.00, 5, 50, 'نشط')"
    
    ' إدخال وصفات المنتجات
    db.Execute "INSERT INTO وصفات_المنتجات (كود_الوصفة, كود_المنتج, اسم_الوصفة, كمية_الانتاج, حالة_الوصفة, تاريخ_الاضافة) VALUES " & _
               "('RCP001', 'FP001', 'وصفة الزيتون المشوي باللبنة', 10, 'نشط', Date()), " & _
               "('RCP002', 'FP002', 'وصفة المعمول بالجوز', 5, 'نشط', Date()), " & _
               "('RCP003', 'FP003', 'وصفة المعمول بعين الجمل', 5, 'نشط', Date())"
    
    ' إدخال تفاصيل الوصفات
    db.Execute "INSERT INTO تفاصيل_الوصفات (كود_الوصفة, كود_المادة, الكمية_المطلوبة, ملاحظات) VALUES " & _
               "('RCP001', 'RM001', 8, 'زيتون أخضر طازج'), " & _
               "('RCP001', 'RM002', 2, 'لبنة طبيعية'), " & _
               "('RCP001', 'RM007', 0.5, 'للشوي'), " & _
               "('RCP002', 'RM005', 3, 'دقيق للعجين'), " & _
               "('RCP002', 'RM003', 2, 'جوز للحشو'), " & _
               "('RCP002', 'RM006', 1, 'سكر للطعم'), " & _
               "('RCP003', 'RM005', 3, 'دقيق للعجين'), " & _
               "('RCP003', 'RM004', 2, 'عين جمل للحشو'), " & _
               "('RCP003', 'RM006', 1, 'سكر للطعم')"
    
    ' إدخال أرصدة افتتاحية للمواد الخام
    db.Execute "INSERT INTO ارصدة_المواد_الخام (كود_المادة, كود_المخزن, الكمية_المتاحة, متوسط_التكلفة, القيمة_الاجمالية, تاريخ_اخر_تحديث) VALUES " & _
               "('RM001', 'WH001', 100, 8.50, 850, Date()), " & _
               "('RM002', 'WH003', 50, 15.00, 750, Date()), " & _
               "('RM003', 'WH001', 30, 25.00, 750, Date()), " & _
               "('RM004', 'WH001', 25, 30.00, 750, Date()), " & _
               "('RM005', 'WH001', 80, 3.50, 280, Date()), " & _
               "('RM006', 'WH001', 40, 4.00, 160, Date()), " & _
               "('RM007', 'WH001', 20, 35.00, 700, Date())"
    
    db.Close
    
    MsgBox "تم إدخال البيانات التجريبية بنجاح!", vbInformation, "البيانات التجريبية"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إدخال البيانات التجريبية: " & Err.Description, vbCritical, "خطأ"
    If Not db Is Nothing Then db.Close
End Sub

' إجراء اختبار النظام
Public Sub اختبار_النظام()
    On Error GoTo ErrorHandler
    
    MsgBox "سيتم الآن اختبار النظام بإنشاء عمليات تجريبية...", vbInformation, "اختبار النظام"
    
    ' اختبار فاتورة مشتريات
    Call اختبار_فاتورة_مشتريات
    
    ' اختبار أمر إنتاج
    Call اختبار_امر_انتاج
    
    ' اختبار فاتورة مبيعات
    Call اختبار_فاتورة_مبيعات
    
    MsgBox "تم اختبار النظام بنجاح!" & vbCrLf & _
           "تم إنشاء عمليات تجريبية لجميع أجزاء النظام.", vbInformation, "اكتمل الاختبار"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء اختبار النظام: " & Err.Description, vbCritical, "خطأ"
End Sub

' اختبار فاتورة مشتريات
Private Sub اختبار_فاتورة_مشتريات()
    Dim db As DAO.Database
    Set db = CurrentDb()
    
    ' إنشاء فاتورة مشتريات تجريبية
    db.Execute "INSERT INTO فواتير_المشتريات (رقم_الفاتورة, كود_المورد, تاريخ_الفاتورة, رقم_فاتورة_المورد, كود_المخزن, اجمالي_الفاتورة, الخصم, الضريبة, صافي_الفاتورة, حالة_الفاتورة) VALUES " & _
               "(1, 'SUP001', Date(), 'INV-001', 'WH001', 1000, 50, 95, 1045, 'مؤكدة')"
    
    ' إضافة تفاصيل الفاتورة
    db.Execute "INSERT INTO تفاصيل_فواتير_المشتريات (رقم_الفاتورة, كود_المادة, الكمية, سعر_الوحدة, القيمة_الاجمالية) VALUES " & _
               "(1, 'RM001', 50, 8.00, 400), " & _
               "(1, 'RM007', 10, 35.00, 350), " & _
               "(1, 'RM002', 15, 16.00, 240)"
    
    db.Close
End Sub

' اختبار أمر إنتاج
Private Sub اختبار_امر_انتاج()
    Dim db As DAO.Database
    Set db = CurrentDb()
    
    ' إنشاء أمر إنتاج تجريبي
    db.Execute "INSERT INTO اوامر_الانتاج (رقم_امر_الانتاج, كود_المنتج, كود_الوصفة, كمية_الانتاج, تاريخ_الامر, تاريخ_البدء, حالة_الامر, كود_المخزن, تكلفة_المواد_الخام, تكلفة_العمالة, التكاليف_المباشرة, التكاليف_غير_المباشرة, اجمالي_التكلفة) VALUES " & _
               "(1, 'FP001', 'RCP001', 20, Date(), Date(), 'مكتمل', 'WH002', 340, 50, 30, 20, 440)"
    
    ' إضافة تكاليف إضافية
    db.Execute "INSERT INTO تكاليف_اوامر_الانتاج (رقم_امر_الانتاج, كود_نوع_التكلفة, قيمة_التكلفة, وصف_التكلفة, تاريخ_التكلفة) VALUES " & _
               "(1, 'LAB001', 50, 'عمالة مباشرة للإنتاج', Date()), " & _
               "(1, 'GAS001', 30, 'غاز للشوي', Date()), " & _
               "(1, 'ELC001', 20, 'كهرباء', Date())"
    
    db.Close
End Sub

' اختبار فاتورة مبيعات
Private Sub اختبار_فاتورة_مبيعات()
    Dim db As DAO.Database
    Set db = CurrentDb()
    
    ' إنشاء فاتورة مبيعات تجريبية
    db.Execute "INSERT INTO فواتير_المبيعات (رقم_الفاتورة, كود_العميل, تاريخ_الفاتورة, كود_المخزن, اجمالي_الفاتورة, الخصم, الضريبة, صافي_الفاتورة, حالة_الفاتورة, طريقة_الدفع) VALUES " & _
               "(1, 'CUS001', Date(), 'WH002', 900, 0, 90, 990, 'مؤكدة', 'نقدي')"
    
    ' إضافة تفاصيل الفاتورة
    db.Execute "INSERT INTO تفاصيل_فواتير_المبيعات (رقم_الفاتورة, كود_المنتج, الكمية, سعر_الوحدة, تكلفة_الوحدة, القيمة_الاجمالية, اجمالي_التكلفة) VALUES " & _
               "(1, 'FP001', 20, 45.00, 22.00, 900, 440)"
    
    db.Close
End Sub

' إجراء إنشاء تقارير النظام
Public Sub انشاء_تقارير_النظام()
    On Error GoTo ErrorHandler
    
    ' سيتم إنشاء التقارير باستخدام معالج التقارير في Access
    MsgBox "لإنشاء التقارير، استخدم معالج التقارير في Access مع الاستعلامات المحفوظة.", vbInformation, "التقارير"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء التقارير: " & Err.Description, vbCritical, "خطأ"
End Sub

' إجراء نسخ احتياطي للنظام
Public Sub انشاء_نسخة_احتياطية()
    On Error GoTo ErrorHandler
    
    Dim مسار_النسخة As String
    مسار_النسخة = CurrentProject.Path & "\نسخة_احتياطية_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".accdb"
    
    ' نسخ قاعدة البيانات
    FileCopy CurrentProject.FullName, مسار_النسخة
    
    MsgBox "تم إنشاء نسخة احتياطية بنجاح في:" & vbCrLf & مسار_النسخة, vbInformation, "النسخ الاحتياطي"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النسخة الاحتياطية: " & Err.Description, vbCritical, "خطأ"
End Sub

' إجراء تحسين أداء النظام
Public Sub تحسين_اداء_النظام()
    On Error GoTo ErrorHandler
    
    ' ضغط وإصلاح قاعدة البيانات
    Application.CompactRepair CurrentProject.FullName, CurrentProject.FullName & "_temp"
    
    MsgBox "تم تحسين أداء النظام بنجاح!", vbInformation, "تحسين الأداء"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تحسين الأداء: " & Err.Description, vbCritical, "خطأ"
End Sub
