-- الفهارس والقيود والبيانات الأساسية
-- نظام محاسبة التكاليف لمصنع المواد الغذائية

-- ========================================
-- إنشاء الفهارس لتحسين الأداء
-- ========================================

-- فهارس جدول المواد الخام
CREATE INDEX idx_مواد_خام_اسم ON المواد_الخام(اسم_المادة);
CREATE INDEX idx_مواد_خام_حالة ON المواد_الخام(حالة_المادة);

-- فهارس جدول المنتجات التامة
CREATE INDEX idx_منتجات_اسم ON المنتجات_التامة(اسم_المنتج);
CREATE INDEX idx_منتجات_حالة ON المنتجات_التامة(حالة_المنتج);

-- فهارس جدول الموردين
CREATE INDEX idx_موردين_اسم ON الموردين(اسم_المورد);
CREATE INDEX idx_موردين_حالة ON الموردين(حالة_المورد);

-- فهارس جدول العملاء
CREATE INDEX idx_عملاء_اسم ON العملاء(اسم_العميل);
CREATE INDEX idx_عملاء_حالة ON العملاء(حالة_العميل);

-- فهارس جدول حركات المخزون
CREATE INDEX idx_حركات_تاريخ ON حركات_المخزون(تاريخ_الحركة);
CREATE INDEX idx_حركات_نوع ON حركات_المخزون(نوع_الحركة);
CREATE INDEX idx_حركات_مادة ON حركات_المخزون(كود_المادة);

-- فهارس جدول أوامر الإنتاج
CREATE INDEX idx_اوامر_تاريخ ON اوامر_الانتاج(تاريخ_الامر);
CREATE INDEX idx_اوامر_حالة ON اوامر_الانتاج(حالة_الامر);

-- فهارس جدول فواتير المشتريات
CREATE INDEX idx_مشتريات_تاريخ ON فواتير_المشتريات(تاريخ_الفاتورة);
CREATE INDEX idx_مشتريات_مورد ON فواتير_المشتريات(كود_المورد);

-- فهارس جدول فواتير المبيعات
CREATE INDEX idx_مبيعات_تاريخ ON فواتير_المبيعات(تاريخ_الفاتورة);
CREATE INDEX idx_مبيعات_عميل ON فواتير_المبيعات(كود_العميل);

-- ========================================
-- إدراج البيانات الأساسية
-- ========================================

-- إدراج الوحدات الأساسية
INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES
('كيلوجرام', 'كجم', 'وحدة الوزن الأساسية'),
('جرام', 'جم', 'وحدة وزن صغيرة'),
('لتر', 'لتر', 'وحدة الحجم للسوائل'),
('مليلتر', 'مل', 'وحدة حجم صغيرة'),
('قطعة', 'قطعة', 'وحدة العدد'),
('علبة', 'علبة', 'وحدة التعبئة'),
('كيس', 'كيس', 'وحدة التعبئة'),
('صندوق', 'صندوق', 'وحدة التعبئة الكبيرة');

-- إدراج أنواع التكاليف الأساسية
INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES
('أجور العمالة المباشرة', 'عمالة'),
('أجور العمالة غير المباشرة', 'عمالة'),
('استهلاك الآلات', 'غير مباشرة'),
('استهلاك المباني', 'غير مباشرة'),
('الكهرباء', 'غير مباشرة'),
('المياه', 'غير مباشرة'),
('الغاز', 'غير مباشرة'),
('صيانة الآلات', 'غير مباشرة'),
('مواد التنظيف', 'غير مباشرة'),
('مواد التعبئة والتغليف', 'مباشرة'),
('النقل والشحن', 'مباشرة'),
('الرقابة والجودة', 'غير مباشرة');

-- إدراج المخازن الأساسية
INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, ملاحظات) VALUES
('مخزن المواد الخام الرئيسي', 'مواد خام', 'المخزن الرئيسي للمواد الخام'),
('مخزن المواد الخام الثانوي', 'مواد خام', 'مخزن احتياطي للمواد الخام'),
('مخزن المنتجات التامة', 'منتجات تامة', 'مخزن المنتجات الجاهزة للبيع'),
('مخزن المنتجات قيد التصنيع', 'منتجات تامة', 'مخزن المنتجات قيد الإنتاج');

-- إدراج بعض المواد الخام الأساسية لمصنع المواد الغذائية
INSERT INTO المواد_الخام (اسم_المادة, كود_الوحدة, الحد_الادنى, الحد_الاقصى, ملاحظات) VALUES
('زيتون أخضر', 1, 100, 1000, 'زيتون أخضر طازج للتصنيع'),
('لبنة', 1, 50, 500, 'لبنة طبيعية للخلط'),
('دقيق', 1, 200, 2000, 'دقيق أبيض فاخر'),
('سكر', 1, 100, 1000, 'سكر أبيض ناعم'),
('زبدة', 1, 50, 500, 'زبدة طبيعية'),
('بيض', 5, 100, 1000, 'بيض طازج'),
('جوز مقشر', 1, 20, 200, 'جوز مقشر ومنظف'),
('عين الجمل', 1, 20, 200, 'عين الجمل مقشر'),
('خميرة', 2, 5, 50, 'خميرة فورية'),
('ملح', 1, 10, 100, 'ملح طعام'),
('زيت نباتي', 3, 50, 500, 'زيت نباتي للطبخ'),
('فانيليا', 4, 1, 10, 'خلاصة الفانيليا'),
('بيكنج باودر', 2, 2, 20, 'بيكنج باودر للخبز');

-- إدراج المنتجات التامة الأساسية
INSERT INTO المنتجات_التامة (اسم_المنتج, كود_الوحدة, سعر_البيع, الحد_الادنى, الحد_الاقصى, ملاحظات) VALUES
('زيتون أخضر مشوي باللبنة - 500جم', 7, 25.00, 50, 500, 'زيتون أخضر مشوي مع اللبنة معبأ في أكياس 500 جرام'),
('زيتون أخضر مشوي باللبنة - 1كجم', 7, 45.00, 30, 300, 'زيتون أخضر مشوي مع اللبنة معبأ في أكياس 1 كيلو'),
('معمول بالجوز - صغير', 8, 35.00, 20, 200, 'معمول بالجوز في صندوق صغير'),
('معمول بالجوز - كبير', 8, 60.00, 10, 100, 'معمول بالجوز في صندوق كبير'),
('معمول بعين الجمل - صغير', 8, 40.00, 20, 200, 'معمول بعين الجمل في صندوق صغير'),
('معمول بعين الجمل - كبير', 8, 70.00, 10, 100, 'معمول بعين الجمل في صندوق كبير');

-- ========================================
-- إنشاء وصفات المنتجات الأساسية
-- ========================================

-- وصفة زيتون أخضر مشوي باللبنة - 500جم
INSERT INTO وصفات_المنتجات (كود_المنتج, اسم_الوصفة, كمية_الانتاج, ملاحظات) VALUES
(1, 'وصفة زيتون أخضر مشوي باللبنة - 500جم', 10, 'إنتاج 10 أكياس في الدفعة الواحدة');

-- تفاصيل وصفة زيتون أخضر مشوي باللبنة - 500جم
INSERT INTO تفاصيل_الوصفات (كود_الوصفة, كود_المادة, الكمية_المطلوبة, ملاحظات) VALUES
(1, 1, 3.5, 'زيتون أخضر - 3.5 كجم'),
(1, 2, 1.5, 'لبنة - 1.5 كجم'),
(1, 11, 0.2, 'زيت نباتي - 200 مل'),
(1, 10, 0.05, 'ملح - 50 جرام');

-- وصفة معمول بالجوز - صغير
INSERT INTO وصفات_المنتجات (كود_المنتج, اسم_الوصفة, كمية_الانتاج, ملاحظات) VALUES
(3, 'وصفة معمول بالجوز - صغير', 5, 'إنتاج 5 صناديق في الدفعة الواحدة');

-- تفاصيل وصفة معمول بالجوز - صغير
INSERT INTO تفاصيل_الوصفات (كود_الوصفة, كود_المادة, الكمية_المطلوبة, ملاحظات) VALUES
(2, 3, 2.0, 'دقيق - 2 كجم'),
(2, 4, 0.5, 'سكر - 500 جرام'),
(2, 5, 0.3, 'زبدة - 300 جرام'),
(2, 6, 10, 'بيض - 10 قطع'),
(2, 7, 1.0, 'جوز مقشر - 1 كجم'),
(2, 9, 20, 'خميرة - 20 جرام'),
(2, 12, 10, 'فانيليا - 10 مل'),
(2, 13, 10, 'بيكنج باودر - 10 جرام');

-- ========================================
-- إنشاء مشغلات (Triggers) لتحديث الأرصدة
-- ========================================

-- مشغل لتحديث أرصدة المواد الخام عند إضافة حركة مخزون
-- ملاحظة: في Access سيتم تنفيذ هذا من خلال VBA أو الاستعلامات

-- ========================================
-- استعلامات أساسية للتقارير
-- ========================================

-- استعلام أرصدة المواد الخام الحالية
CREATE VIEW عرض_ارصدة_المواد_الخام AS
SELECT 
    م.اسم_المادة,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    ر.متوسط_التكلفة,
    ر.القيمة_الاجمالية,
    و.اسم_الوحدة
FROM ارصدة_المواد_الخام ر
INNER JOIN المواد_الخام م ON ر.كود_المادة = م.كود_المادة
INNER JOIN المخازن مخ ON ر.كود_المخزن = مخ.كود_المخزن
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة
WHERE ر.الكمية_المتاحة > 0;

-- استعلام أرصدة المنتجات التامة الحالية
CREATE VIEW عرض_ارصدة_المنتجات_التامة AS
SELECT 
    م.اسم_المنتج,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    ر.متوسط_التكلفة,
    ر.القيمة_الاجمالية,
    و.اسم_الوحدة
FROM ارصدة_المنتجات_التامة ر
INNER JOIN المنتجات_التامة م ON ر.كود_المنتج = م.كود_المنتج
INNER JOIN المخازن مخ ON ر.كود_المخزن = مخ.كود_المخزن
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة
WHERE ر.الكمية_المتاحة > 0;

-- استعلام تكاليف أوامر الإنتاج
CREATE VIEW عرض_تكاليف_اوامر_الانتاج AS
SELECT 
    أ.رقم_امر_الانتاج,
    م.اسم_المنتج,
    أ.كمية_الانتاج,
    أ.تاريخ_الامر,
    أ.حالة_الامر,
    أ.تكلفة_المواد_الخام,
    أ.تكلفة_العمالة,
    أ.التكاليف_المباشرة,
    أ.التكاليف_غير_المباشرة,
    أ.اجمالي_التكلفة,
    (أ.اجمالي_التكلفة / أ.كمية_الانتاج) AS تكلفة_الوحدة
FROM اوامر_الانتاج أ
INNER JOIN المنتجات_التامة م ON أ.كود_المنتج = م.كود_المنتج;
