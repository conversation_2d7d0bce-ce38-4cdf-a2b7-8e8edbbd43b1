Sub اعداد_جدول_مراكز_التكلفة(ws As Worksheet)
    ' إعداد جدول مراكز التكلفة
    With ws
        .Range("A1").Value = "جدول مراكز التكلفة"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:D1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "كود المركز"
        .Range("B3").Value = "اسم المركز"
        .Range("C3").Value = "نوع المركز"
        .Range("D3").Value = "ملاحظات"
        
        ' تنسيق العناوين
        .Range("A3:D3").Font.Bold = True
        .Range("A3:D3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:D3").Borders.LineStyle = xlContinuous
        
        ' إضافة مراكز التكلفة
        .Range("A4").Value = "CC001"
        .Range("B4").Value = "قسم الإنتاج"
        .Range("C4").Value = "مركز إنتاج"
        .Range("D4").Value = "المركز الرئيسي للإنتاج"
        
        .Range("A5").Value = "CC002"
        .Range("B5").Value = "قسم التعبئة والتغليف"
        .Range("C5").Value = "مركز إنتاج"
        .Range("D5").Value = "تعبئة المنتجات النهائية"
        
        .Range("A6").Value = "CC003"
        .Range("B6").Value = "قسم الجودة"
        .Range("C6").Value = "مركز خدمي"
        .Range("D6").Value = "مراقبة الجودة والاختبارات"
        
        .Range("A7").Value = "CC004"
        .Range("B7").Value = "قسم المخازن"
        .Range("C7").Value = "مركز خدمي"
        .Range("D7").Value = "إدارة المخازن والمخزون"
        
        .Range("A8").Value = "CC005"
        .Range("B8").Value = "الإدارة العامة"
        .Range("C8").Value = "مركز إداري"
        .Range("D8").Value = "التكاليف الإدارية العامة"
        
        ' تنسيق الجدول
        .Range("A3:D10").Borders.LineStyle = xlContinuous
        .Columns("A:D").AutoFit
    End With
End Sub

Sub اعداد_جدول_المشتريات(ws As Worksheet)
    ' إعداد جدول المشتريات
    With ws
        .Range("A1").Value = "سجل المشتريات"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:H1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "رقم الفاتورة"
        .Range("B3").Value = "التاريخ"
        .Range("C3").Value = "كود المورد"
        .Range("D3").Value = "كود المادة"
        .Range("E3").Value = "الكمية"
        .Range("F3").Value = "سعر الوحدة"
        .Range("G3").Value = "إجمالي القيمة"
        .Range("H3").Value = "ملاحظات"
        
        ' تنسيق العناوين
        .Range("A3:H3").Font.Bold = True
        .Range("A3:H3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:H3").Borders.LineStyle = xlContinuous
        
        ' إضافة صيغة حساب إجمالي القيمة
        .Range("G4").Formula = "=E4*F4"
        
        ' إضافة بيانات تجريبية
        .Range("A4").Value = "PUR001"
        .Range("B4").Value = Date
        .Range("C4").Value = "SUP001"
        .Range("D4").Value = "RM001"
        .Range("E4").Value = 100
        .Range("F4").Value = 5.5
        .Range("H4").Value = "شحنة زيتون طازج"
        
        ' تنسيق الجدول
        .Range("A3:H20").Borders.LineStyle = xlContinuous
        .Columns("A:H").AutoFit
    End With
End Sub

Sub اعداد_جدول_مخزون_المواد_الخام(ws As Worksheet)
    ' إعداد جدول مخزون المواد الخام
    With ws
        .Range("A1").Value = "مخزون المواد الخام"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:G1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "كود المادة"
        .Range("B3").Value = "اسم المادة"
        .Range("C3").Value = "الرصيد الحالي"
        .Range("D3").Value = "متوسط التكلفة"
        .Range("E3").Value = "قيمة المخزون"
        .Range("F3").Value = "آخر تحديث"
        .Range("G3").Value = "حالة المخزون"
        
        ' تنسيق العناوين
        .Range("A3:G3").Font.Bold = True
        .Range("A3:G3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:G3").Borders.LineStyle = xlContinuous
        
        ' إضافة صيغ حساب قيمة المخزون
        .Range("E4").Formula = "=C4*D4"
        
        ' إضافة بيانات تجريبية
        .Range("A4").Value = "RM001"
        .Range("B4").Value = "زيتون أخضر"
        .Range("C4").Value = 150
        .Range("D4").Value = 5.5
        .Range("F4").Value = Date
        .Range("G4").Value = "متوفر"
        
        .Range("A5").Value = "RM002"
        .Range("B5").Value = "لبنة طبيعية"
        .Range("C5").Value = 75
        .Range("D5").Value = 8.0
        .Range("F5").Value = Date
        .Range("G5").Value = "متوفر"
        
        ' تنسيق الجدول
        .Range("A3:G20").Borders.LineStyle = xlContinuous
        .Columns("A:G").AutoFit
    End With
End Sub

Sub اعداد_جدول_الوصفات(ws As Worksheet)
    ' إعداد جدول الوصفات
    With ws
        .Range("A1").Value = "وصفات المنتجات"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "كود المنتج"
        .Range("B3").Value = "كود المادة الخام"
        .Range("C3").Value = "اسم المادة"
        .Range("D3").Value = "الكمية المطلوبة"
        .Range("E3").Value = "وحدة القياس"
        .Range("F3").Value = "ملاحظات"
        
        ' تنسيق العناوين
        .Range("A3:F3").Font.Bold = True
        .Range("A3:F3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:F3").Borders.LineStyle = xlContinuous
        
        ' وصفة الزيتون الأخضر المشوي باللبنة
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "RM001"
        .Range("C4").Value = "زيتون أخضر"
        .Range("D4").Value = 0.3
        .Range("E4").Value = "كيلوغرام"
        .Range("F4").Value = "زيتون مشوي"
        
        .Range("A5").Value = "FG001"
        .Range("B5").Value = "RM002"
        .Range("C5").Value = "لبنة طبيعية"
        .Range("D5").Value = 0.2
        .Range("E5").Value = "كيلوغرام"
        .Range("F5").Value = "لبنة طازجة"
        
        ' وصفة المعمول بالجوز
        .Range("A6").Value = "FG002"
        .Range("B6").Value = "RM005"
        .Range("C6").Value = "دقيق أبيض"
        .Range("D6").Value = 0.15
        .Range("E6").Value = "كيلوغرام"
        .Range("F6").Value = "دقيق ممتاز"
        
        .Range("A7").Value = "FG002"
        .Range("B7").Value = "RM003"
        .Range("C7").Value = "جوز مقشر"
        .Range("D7").Value = 0.08
        .Range("E7").Value = "كيلوغرام"
        .Range("F7").Value = "حشوة الجوز"
        
        .Range("A8").Value = "FG002"
        .Range("B8").Value = "RM006"
        .Range("C8").Value = "سمن نباتي"
        .Range("D8").Value = 0.02
        .Range("E8").Value = "كيلوغرام"
        .Range("F8").Value = "للعجين"
        
        ' وصفة المعمول بعين الجمل
        .Range("A9").Value = "FG003"
        .Range("B9").Value = "RM005"
        .Range("C9").Value = "دقيق أبيض"
        .Range("D9").Value = 0.15
        .Range("E9").Value = "كيلوغرام"
        .Range("F9").Value = "دقيق ممتاز"
        
        .Range("A10").Value = "FG003"
        .Range("B10").Value = "RM004"
        .Range("C10").Value = "عين الجمل"
        .Range("D10").Value = 0.08
        .Range("E10").Value = "كيلوغرام"
        .Range("F10").Value = "حشوة عين الجمل"
        
        ' تنسيق الجدول
        .Range("A3:F20").Borders.LineStyle = xlContinuous
        .Columns("A:F").AutoFit
    End With
End Sub
