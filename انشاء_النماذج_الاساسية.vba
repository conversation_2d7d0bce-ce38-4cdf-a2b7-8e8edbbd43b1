' كود VBA لإنشاء النماذج الأساسية
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء جميع النماذج الأساسية
Public Sub انشاء_النماذج_الاساسية()
    On Error GoTo ErrorHandler
    
    ' إنشاء النماذج الأساسية
    Call انشاء_نموذج_الوحدات
    Call انشاء_نموذج_الموردين
    Call انشاء_نموذج_العملاء
    Call انشاء_نموذج_المواد_الخام
    Call انشاء_نموذج_المنتجات_التامة
    Call انشاء_نموذج_المخازن
    Call انشاء_نموذج_انواع_التكاليف
    
    MsgBox "تم إنشاء النماذج الأساسية بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النماذج: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء نموذج الوحدات
Private Sub انشاء_نموذج_الوحدات()
    Dim frm As Form
    Dim ctl As Control
    
    ' إنشاء النموذج
    Set frm = CreateForm()
    frm.Name = "نموذج_الوحدات"
    frm.Caption = "إدارة الوحدات"
    frm.RecordSource = "الوحدات"
    frm.DefaultView = 0 ' Single Form
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' إضافة عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 6000, 600)
    ctl.Caption = "إدارة الوحدات"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2 ' Center
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' إضافة الحقول
    ' كود الوحدة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 2000, 300)
    ctl.Caption = "كود الوحدة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 1000, 1500, 300)
    ctl.ControlSource = "كود_الوحدة"
    ctl.Enabled = False
    
    ' اسم الوحدة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1500, 2000, 300)
    ctl.Caption = "اسم الوحدة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 1500, 3000, 300)
    ctl.ControlSource = "اسم_الوحدة"
    
    ' اختصار الوحدة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2000, 2000, 300)
    ctl.Caption = "اختصار الوحدة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 2000, 1500, 300)
    ctl.ControlSource = "اختصار_الوحدة"
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2500, 2000, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 2500, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2 ' Vertical
    
    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 3500)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج الموردين
Private Sub انشاء_نموذج_الموردين()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_الموردين"
    frm.Caption = "إدارة الموردين"
    frm.RecordSource = "الموردين"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة الموردين"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' الحقول - العمود الأول
    ' كود المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود المورد:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_المورد"
    ctl.Enabled = False
    
    ' اسم المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "اسم المورد:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "اسم_المورد"
    
    ' العنوان
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "العنوان:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1800, 3000, 300)
    ctl.ControlSource = "العنوان"
    
    ' الهاتف
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "الهاتف:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 2000, 300)
    ctl.ControlSource = "الهاتف"
    
    ' الجوال
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "الجوال:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 2000, 300)
    ctl.ControlSource = "الجوال"
    
    ' البريد الإلكتروني
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3000, 1800, 300)
    ctl.Caption = "البريد الإلكتروني:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3000, 3000, 300)
    ctl.ControlSource = "البريد_الالكتروني"
    
    ' الرقم الضريبي
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1800, 300)
    ctl.Caption = "الرقم الضريبي:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3400, 2000, 300)
    ctl.ControlSource = "الرقم_الضريبي"
    
    ' حالة المورد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3800, 1800, 300)
    ctl.Caption = "حالة المورد:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 3800, 1500, 300)
    ctl.ControlSource = "حالة_المورد"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;معلق"
    
    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4200, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4200, 1500, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4600, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4600, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 5600)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج العملاء
Private Sub انشاء_نموذج_العملاء()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_العملاء"
    frm.Caption = "إدارة العملاء"
    frm.RecordSource = "العملاء"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة العملاء"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' الحقول (نفس تخطيط الموردين)
    ' كود العميل
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود العميل:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_العميل"
    ctl.Enabled = False
    
    ' اسم العميل
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "اسم العميل:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "اسم_العميل"
    
    ' العنوان
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "العنوان:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1800, 3000, 300)
    ctl.ControlSource = "العنوان"
    
    ' الهاتف
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "الهاتف:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 2000, 300)
    ctl.ControlSource = "الهاتف"
    
    ' الجوال
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "الجوال:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 2000, 300)
    ctl.ControlSource = "الجوال"
    
    ' البريد الإلكتروني
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3000, 1800, 300)
    ctl.Caption = "البريد الإلكتروني:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3000, 3000, 300)
    ctl.ControlSource = "البريد_الالكتروني"
    
    ' الرقم الضريبي
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1800, 300)
    ctl.Caption = "الرقم الضريبي:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3400, 2000, 300)
    ctl.ControlSource = "الرقم_الضريبي"
    
    ' حالة العميل
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3800, 1800, 300)
    ctl.Caption = "حالة العميل:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 3800, 1500, 300)
    ctl.ControlSource = "حالة_العميل"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;معلق"
    
    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4200, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4200, 1500, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4600, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4600, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 5600)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج المواد الخام
Private Sub انشاء_نموذج_المواد_الخام()
    Dim frm As Form
    Dim ctl As Control

    Set frm = CreateForm()
    frm.Name = "نموذج_المواد_الخام"
    frm.Caption = "إدارة المواد الخام"
    frm.RecordSource = "المواد_الخام"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True

    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة المواد الخام"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)

    ' كود المادة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود المادة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_المادة"
    ctl.Enabled = False

    ' اسم المادة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "اسم المادة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "اسم_المادة"

    ' وحدة القياس
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "وحدة القياس:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1800, 2000, 300)
    ctl.ControlSource = "كود_الوحدة"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_الوحدة, اسم_الوحدة FROM الوحدات ORDER BY اسم_الوحدة"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;2000"
    ctl.BoundColumn = 1

    ' الحد الأدنى
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "الحد الأدنى:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 1500, 300)
    ctl.ControlSource = "الحد_الادنى"
    ctl.Format = "Standard"

    ' الحد الأقصى
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "الحد الأقصى:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 1500, 300)
    ctl.ControlSource = "الحد_الاقصى"
    ctl.Format = "Standard"

    ' متوسط التكلفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3000, 1800, 300)
    ctl.Caption = "متوسط التكلفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3000, 1500, 300)
    ctl.ControlSource = "متوسط_التكلفة"
    ctl.Format = "Currency"
    ctl.Enabled = False

    ' حالة المادة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1800, 300)
    ctl.Caption = "حالة المادة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 3400, 1500, 300)
    ctl.ControlSource = "حالة_المادة"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;متوقف"

    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3800, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3800, 1500, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False

    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4200, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4200, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2

    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 5200)

    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج المنتجات التامة
Private Sub انشاء_نموذج_المنتجات_التامة()
    Dim frm As Form
    Dim ctl As Control

    Set frm = CreateForm()
    frm.Name = "نموذج_المنتجات_التامة"
    frm.Caption = "إدارة المنتجات التامة"
    frm.RecordSource = "المنتجات_التامة"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True

    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة المنتجات التامة"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)

    ' كود المنتج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود المنتج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_المنتج"
    ctl.Enabled = False

    ' اسم المنتج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "اسم المنتج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "اسم_المنتج"

    ' وحدة القياس
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "وحدة القياس:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1800, 2000, 300)
    ctl.ControlSource = "كود_الوحدة"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_الوحدة, اسم_الوحدة FROM الوحدات ORDER BY اسم_الوحدة"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;2000"
    ctl.BoundColumn = 1

    ' سعر البيع
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "سعر البيع:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 1500, 300)
    ctl.ControlSource = "سعر_البيع"
    ctl.Format = "Currency"

    ' تكلفة الإنتاج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "تكلفة الإنتاج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 1500, 300)
    ctl.ControlSource = "تكلفة_الانتاج"
    ctl.Format = "Currency"
    ctl.Enabled = False

    ' الحد الأدنى
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3000, 1800, 300)
    ctl.Caption = "الحد الأدنى:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3000, 1500, 300)
    ctl.ControlSource = "الحد_الادنى"
    ctl.Format = "Standard"

    ' الحد الأقصى
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1800, 300)
    ctl.Caption = "الحد الأقصى:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3400, 1500, 300)
    ctl.ControlSource = "الحد_الاقصى"
    ctl.Format = "Standard"

    ' حالة المنتج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3800, 1800, 300)
    ctl.Caption = "حالة المنتج:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 3800, 1500, 300)
    ctl.ControlSource = "حالة_المنتج"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;متوقف"

    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4200, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4200, 1500, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False

    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 4600, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 4600, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2

    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 5600)

    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج المخازن
Private Sub انشاء_نموذج_المخازن()
    Dim frm As Form
    Dim ctl As Control

    Set frm = CreateForm()
    frm.Name = "نموذج_المخازن"
    frm.Caption = "إدارة المخازن"
    frm.RecordSource = "المخازن"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True

    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة المخازن"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)

    ' كود المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود المخزن:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_المخزن"
    ctl.Enabled = False

    ' اسم المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "اسم المخزن:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "اسم_المخزن"

    ' نوع المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "نوع المخزن:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1800, 2000, 300)
    ctl.ControlSource = "نوع_المخزن"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "مواد خام;منتجات تامة;مواد تعبئة;مواد مساعدة"

    ' العنوان
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "العنوان:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 3000, 300)
    ctl.ControlSource = "العنوان"

    ' المسؤول
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2600, 1800, 300)
    ctl.Caption = "المسؤول:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2600, 2500, 300)
    ctl.ControlSource = "المسؤول"

    ' حالة المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3000, 1800, 300)
    ctl.Caption = "حالة المخزن:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 3000, 1500, 300)
    ctl.ControlSource = "حالة_المخزن"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;صيانة"

    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3400, 1500, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False

    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3800, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3800, 4000, 800)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2

    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 4800)

    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج أنواع التكاليف
Private Sub انشاء_نموذج_انواع_التكاليف()
    Dim frm As Form
    Dim ctl As Control

    Set frm = CreateForm()
    frm.Name = "نموذج_انواع_التكاليف"
    frm.Caption = "إدارة أنواع التكاليف"
    frm.RecordSource = "انواع_التكاليف"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True

    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 8000, 600)
    ctl.Caption = "إدارة أنواع التكاليف"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)

    ' كود نوع التكلفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 2000, 300)
    ctl.Caption = "كود نوع التكلفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 1000, 1200, 300)
    ctl.ControlSource = "كود_نوع_التكلفة"
    ctl.Enabled = False

    ' اسم نوع التكلفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 2000, 300)
    ctl.Caption = "اسم نوع التكلفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2600, 1400, 3000, 300)
    ctl.ControlSource = "اسم_نوع_التكلفة"

    ' تصنيف التكلفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 2000, 300)
    ctl.Caption = "تصنيف التكلفة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2600, 1800, 2000, 300)
    ctl.ControlSource = "تصنيف_التكلفة"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "مباشرة;غير مباشرة;عمالة;إدارية"

    ' حالة النوع
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 2000, 300)
    ctl.Caption = "حالة النوع:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2600, 2200, 1500, 300)
    ctl.ControlSource = "حالة_النوع"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط"

    ' أزرار التحكم
    Call اضافة_ازرار_التحكم(frm, 2800)

    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إجراء إضافة أزرار التحكم للنماذج
Private Sub اضافة_ازرار_التحكم(frm As Form, yPosition As Long)
    Dim ctl As Control

    ' زر جديد
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 500, yPosition, 1200, 400)
    ctl.Caption = "جديد"
    ctl.Name = "btn_جديد"

    ' زر حفظ
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1800, yPosition, 1200, 400)
    ctl.Caption = "حفظ"
    ctl.Name = "btn_حفظ"

    ' زر حذف
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3100, yPosition, 1200, 400)
    ctl.Caption = "حذف"
    ctl.Name = "btn_حذف"

    ' زر إغلاق
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 4400, yPosition, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btn_اغلاق"
End Sub
