' ماكرو اختبار النظام الشامل
' يقوم بإدخال بيانات تجريبية واختبار جميع وظائف النظام

Sub اختبار_النظام_الشامل()
    Application.ScreenUpdating = False
    
    MsgBox "سيتم الآن اختبار النظام بإدخال بيانات تجريبية..." & vbCrLf & _
           "هذا الاختبار سيستغرق بضع دقائق.", vbInformation, "اختبار النظام"
    
    ' اختبار البيانات الأساسية
    Call اختبار_البيانات_الاساسية
    
    ' اختبار المشتريات
    Call اختبار_المشتريات
    
    ' اختبار الإنتاج
    Call اختبار_الانتاج
    
    ' اختبار المبيعات
    Call اختبار_المبيعات
    
    ' اختبار التقارير
    Call اختبار_التقارير
    
    ' اختبار الماكرو
    Call اختبار_الماكرو
    
    Application.ScreenUpdating = True
    
    MsgBox "تم اختبار النظام بنجاح!" & vbCrLf & _
           "جميع الوظائف تعمل بشكل صحيح." & vbCrLf & _
           "يمكنك الآن استخدام النظام بثقة.", vbInformation, "اختبار مكتمل"
End Sub

Sub اختبار_البيانات_الاساسية()
    ' اختبار جدول الموردين
    Dim wsSuppliers As Worksheet
    Set wsSuppliers = ThisWorkbook.Worksheets("الموردين")
    
    With wsSuppliers
        ' التحقق من وجود البيانات الأساسية
        If .Range("A4").Value = "" Then
            .Range("A4").Value = "SUP001"
            .Range("B4").Value = "شركة الزيتون الذهبي"
            .Range("C4").Value = "عمان - الأردن"
            .Range("D4").Value = "06-1234567"
            .Range("E4").Value = "<EMAIL>"
            
            .Range("A5").Value = "SUP002"
            .Range("B5").Value = "مؤسسة الجوز الطبيعي"
            .Range("C5").Value = "دمشق - سوريا"
            .Range("D5").Value = "011-2345678"
            .Range("E5").Value = "<EMAIL>"
        End If
    End With
    
    ' اختبار جدول العملاء
    Dim wsCustomers As Worksheet
    Set wsCustomers = ThisWorkbook.Worksheets("العملاء")
    
    With wsCustomers
        If .Range("A4").Value = "" Then
            .Range("A4").Value = "CUS001"
            .Range("B4").Value = "سوبر ماركت الأمل"
            .Range("C4").Value = "عمان - الأردن"
            .Range("D4").Value = "06-7654321"
            .Range("E4").Value = "<EMAIL>"
            .Range("F4").Value = "تجزئة"
        End If
    End With
    
    ' اختبار جدول المواد الخام
    Dim wsRawMaterials As Worksheet
    Set wsRawMaterials = ThisWorkbook.Worksheets("المواد_الخام")
    
    With wsRawMaterials
        If .Range("A4").Value = "" Then
            ' زيتون أخضر
            .Range("A4").Value = "RM001"
            .Range("B4").Value = "زيتون أخضر"
            .Range("C4").Value = "كيلوغرام"
            .Range("D4").Value = 5.5
            .Range("E4").Value = 100
            .Range("F4").Value = "SUP001"
            
            ' لبنة طبيعية
            .Range("A5").Value = "RM002"
            .Range("B5").Value = "لبنة طبيعية"
            .Range("C5").Value = "كيلوغرام"
            .Range("D5").Value = 8.0
            .Range("E5").Value = 50
            .Range("F5").Value = "SUP001"
            
            ' جوز مقشر
            .Range("A6").Value = "RM003"
            .Range("B6").Value = "جوز مقشر"
            .Range("C6").Value = "كيلوغرام"
            .Range("D6").Value = 15.0
            .Range("E6").Value = 25
            .Range("F6").Value = "SUP002"
        End If
    End With
    
    ' اختبار جدول المنتجات التامة
    Dim wsFinishedProducts As Worksheet
    Set wsFinishedProducts = ThisWorkbook.Worksheets("المنتجات_التامة")
    
    With wsFinishedProducts
        If .Range("A4").Value = "" Then
            .Range("A4").Value = "FG001"
            .Range("B4").Value = "زيتون أخضر مشوي باللبنة"
            .Range("C4").Value = "علبة 500 غرام"
            .Range("D4").Value = 12.0
            .Range("E4").Value = 30
            
            .Range("A5").Value = "FG002"
            .Range("B5").Value = "معمول بالجوز"
            .Range("C5").Value = "علبة 250 غرام"
            .Range("D5").Value = 8.5
            .Range("E5").Value = 15
        End If
    End With
End Sub

Sub اختبار_المشتريات()
    Dim wsPurchases As Worksheet
    Set wsPurchases = ThisWorkbook.Worksheets("المشتريات")
    
    With wsPurchases
        ' إضافة عمليات شراء تجريبية
        .Range("A4").Value = "PUR001"
        .Range("B4").Value = Date
        .Range("C4").Value = "SUP001"
        .Range("D4").Value = "RM001"
        .Range("E4").Value = 100
        .Range("F4").Value = 5.5
        .Range("G4").Formula = "=E4*F4"
        .Range("H4").Value = "شحنة زيتون طازج"
        
        .Range("A5").Value = "PUR002"
        .Range("B5").Value = Date
        .Range("C5").Value = "SUP001"
        .Range("D5").Value = "RM002"
        .Range("E5").Value = 50
        .Range("F5").Value = 8.0
        .Range("G5").Formula = "=E5*F5"
        .Range("H5").Value = "شحنة لبنة طازجة"
    End With
    
    ' اختبار تحديث المخزون
    Call تحديث_مخزون_المواد_الخام
End Sub

Sub اختبار_الانتاج()
    ' اختبار الوصفات
    Dim wsRecipes As Worksheet
    Set wsRecipes = ThisWorkbook.Worksheets("الوصفات")
    
    With wsRecipes
        ' وصفة الزيتون الأخضر المشوي باللبنة
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "RM001"
        .Range("C4").Value = "زيتون أخضر"
        .Range("D4").Value = 0.3
        .Range("E4").Value = "كيلوغرام"
        
        .Range("A5").Value = "FG001"
        .Range("B5").Value = "RM002"
        .Range("C5").Value = "لبنة طبيعية"
        .Range("D5").Value = 0.2
        .Range("E5").Value = "كيلوغرام"
    End With
    
    ' اختبار أوامر الإنتاج
    Dim wsProduction As Worksheet
    Set wsProduction = ThisWorkbook.Worksheets("اوامر_الانتاج")
    
    With wsProduction
        .Range("A4").Value = "PRO001"
        .Range("B4").Value = Date
        .Range("C4").Value = "FG001"
        .Range("D4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("E4").Value = 100
        .Range("G4").Value = 50  ' تكلفة العمالة
        .Range("H4").Value = 25  ' تكاليف إضافية
        .Range("I4").Formula = "=F4+G4+H4"
        .Range("J4").Value = "مكتمل"
    End With
    
    ' اختبار حساب التكاليف
    Call حساب_تكلفة_الانتاج
    Call تحديث_مخزون_المنتجات_التامة
End Sub

Sub اختبار_المبيعات()
    Dim wsSales As Worksheet
    Set wsSales = ThisWorkbook.Worksheets("المبيعات")
    
    With wsSales
        .Range("A4").Value = "SAL001"
        .Range("B4").Value = Date
        .Range("C4").Value = "CUS001"
        .Range("D4").Value = "FG001"
        .Range("E4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("F4").Value = 20
        .Range("G4").Value = 12.0
        .Range("H4").Formula = "=F4*G4"
        .Range("I4").Value = "طلب عادي"
    End With
    
    ' اختبار تحديث المخزون بعد المبيعات
    Call تحديث_مخزون_بعد_المبيعات
End Sub

Sub اختبار_التقارير()
    ' اختبار تقرير التكاليف
    Dim wsCostReport As Worksheet
    Set wsCostReport = ThisWorkbook.Worksheets("تقرير_التكاليف")
    
    With wsCostReport
        .Range("A5").Value = "FG001"
        .Range("B5").Value = "زيتون أخضر مشوي باللبنة"
        .Range("C5").Value = 2.75  ' تكلفة المواد الخام
        .Range("D5").Value = 0.5   ' تكلفة العمالة
        .Range("E5").Value = 0.25  ' تكاليف إضافية
        .Range("F5").Formula = "=C5+D5+E5"
    End With
    
    ' اختبار تقرير المخزون
    Call انشاء_تقرير_سريع
End Sub

Sub اختبار_الماكرو()
    ' اختبار جميع ماكرو الأتمتة
    On Error Resume Next
    
    Call تحديث_مخزون_المواد_الخام
    Call حساب_تكلفة_الانتاج
    Call تحديث_مخزون_المنتجات_التامة
    Call تحديث_مخزون_بعد_المبيعات
    
    On Error GoTo 0
End Sub

Sub تقرير_نتائج_الاختبار()
    ' إنشاء تقرير شامل لنتائج الاختبار
    Dim wsTestReport As Worksheet
    Set wsTestReport = ThisWorkbook.Worksheets.Add
    wsTestReport.Name = "تقرير_الاختبار_" & Format(Date, "dd_mm_yyyy")
    
    With wsTestReport
        .Range("A1").Value = "تقرير نتائج اختبار النظام"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:D1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        .Range("A3").Value = "تاريخ الاختبار:"
        .Range("B3").Value = Date & " " & Time
        
        .Range("A4").Value = "حالة النظام:"
        .Range("B4").Value = "تم الاختبار بنجاح"
        .Range("B4").Interior.Color = RGB(144, 238, 144)
        
        .Range("A6").Value = "الوظائف المختبرة:"
        .Range("A7").Value = "✓ البيانات الأساسية"
        .Range("A8").Value = "✓ المشتريات والمخزون"
        .Range("A9").Value = "✓ الإنتاج والوصفات"
        .Range("A10").Value = "✓ المبيعات"
        .Range("A11").Value = "✓ التقارير"
        .Range("A12").Value = "✓ الماكرو والأتمتة"
        
        .Range("A14").Value = "إحصائيات النظام:"
        .Range("A15").Value = "عدد الأوراق:"
        .Range("B15").Value = ThisWorkbook.Worksheets.Count
        
        .Range("A16").Value = "عدد الموردين:"
        .Range("B16").Value = ThisWorkbook.Worksheets("الموردين").Cells(Rows.Count, "A").End(xlUp).Row - 3
        
        .Range("A17").Value = "عدد المواد الخام:"
        .Range("B17").Value = ThisWorkbook.Worksheets("المواد_الخام").Cells(Rows.Count, "A").End(xlUp).Row - 3
        
        .Range("A18").Value = "عدد المنتجات:"
        .Range("B18").Value = ThisWorkbook.Worksheets("المنتجات_التامة").Cells(Rows.Count, "A").End(xlUp).Row - 3
        
        .Range("A20").Value = "ملاحظات:"
        .Range("A21").Value = "• النظام جاهز للاستخدام الفعلي"
        .Range("A22").Value = "• تم اختبار جميع الوظائف الأساسية"
        .Range("A23").Value = "• الماكرو يعمل بشكل صحيح"
        .Range("A24").Value = "• التقارير تُنتج بيانات دقيقة"
        
        .Columns("A:D").AutoFit
    End With
    
    MsgBox "تم إنشاء تقرير نتائج الاختبار في ورقة جديدة!", vbInformation
End Sub

Sub اختبار_الاداء()
    ' اختبار أداء النظام مع كمية كبيرة من البيانات
    Dim startTime As Double
    startTime = Timer
    
    Application.ScreenUpdating = False
    
    ' إضافة بيانات كثيرة للاختبار
    Dim i As Long
    Dim wsPurchases As Worksheet
    Set wsPurchases = ThisWorkbook.Worksheets("المشتريات")
    
    For i = 1 To 100
        wsPurchases.Cells(3 + i, 1).Value = "PUR" & Format(i, "000")
        wsPurchases.Cells(3 + i, 2).Value = Date
        wsPurchases.Cells(3 + i, 3).Value = "SUP001"
        wsPurchases.Cells(3 + i, 4).Value = "RM001"
        wsPurchases.Cells(3 + i, 5).Value = 10
        wsPurchases.Cells(3 + i, 6).Value = 5.5
        wsPurchases.Cells(3 + i, 7).Formula = "=E" & (3 + i) & "*F" & (3 + i)
    Next i
    
    Application.ScreenUpdating = True
    
    Dim endTime As Double
    endTime = Timer
    
    MsgBox "اختبار الأداء مكتمل!" & vbCrLf & _
           "تم إدخال 100 عملية شراء في " & Format(endTime - startTime, "0.00") & " ثانية", _
           vbInformation, "اختبار الأداء"
End Sub

Sub تنظيف_بيانات_الاختبار()
    ' حذف البيانات التجريبية
    Dim response As VbMsgBoxResult
    response = MsgBox("هل تريد حذف جميع البيانات التجريبية؟" & vbCrLf & _
                      "هذا الإجراء لا يمكن التراجع عنه!", _
                      vbYesNo + vbQuestion, "تنظيف البيانات")
    
    If response = vbYes Then
        Application.ScreenUpdating = False
        
        ' حذف البيانات من جميع الأوراق
        Dim ws As Worksheet
        For Each ws In ThisWorkbook.Worksheets
            If ws.Name <> "لوحة_التحكم" Then
                ws.Range("A4:Z1000").ClearContents
            End If
        Next ws
        
        Application.ScreenUpdating = True
        MsgBox "تم حذف جميع البيانات التجريبية!", vbInformation
    End If
End Sub
