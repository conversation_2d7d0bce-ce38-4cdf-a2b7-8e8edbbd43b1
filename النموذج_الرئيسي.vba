' كود VBA لإنشاء النموذج الرئيسي ونظام التنقل
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء إنشاء النموذج الرئيسي
Public Sub انشاء_النموذج_الرئيسي()
    On Error GoTo ErrorHandler
    
    Call انشاء_نموذج_القائمة_الرئيسية
    Call انشاء_نموذج_لوحة_المعلومات
    
    MsgBox "تم إنشاء النموذج الرئيسي بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النموذج الرئيسي: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء نموذج القائمة الرئيسية
Private Sub انشاء_نموذج_القائمة_الرئيسية()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "النموذج_الرئيسي"
    frm.Caption = "نظام محاسبة التكاليف - مصنع المواد الغذائية"
    frm.DefaultView = 0
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = False
    frm.RecordSelectors = False
    frm.DividingLines = False
    frm.ScrollBars = 0
    frm.BorderStyle = 1 ' Thin
    frm.ControlBox = True
    frm.MinMaxButtons = 0 ' None
    
    ' تعيين حجم النموذج
    frm.Width = 14000
    frm.Detail.Height = 9000
    
    ' عنوان النظام
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 14000, 800)
    ctl.Caption = "نظام محاسبة التكاليف لمصنع المواد الغذائية"
    ctl.FontSize = 24
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    ctl.Name = "lbl_عنوان_النظام"
    
    ' شعار أو صورة (اختياري)
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 3000, 600)
    ctl.Caption = "🏭 مصنع المواد الغذائية"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(240, 240, 240)
    ctl.BorderStyle = 1
    
    ' قسم البيانات الأساسية
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 6000, 400)
    ctl.Caption = "البيانات الأساسية"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(200, 230, 255)
    ctl.TextAlign = 2
    
    ' أزرار البيانات الأساسية
    Call انشاء_زر_تنقل(frm, "btn_الوحدات", "الوحدات", 500, 2300, "نموذج_الوحدات")
    Call انشاء_زر_تنقل(frm, "btn_الموردين", "الموردين", 1700, 2300, "نموذج_الموردين")
    Call انشاء_زر_تنقل(frm, "btn_العملاء", "العملاء", 2900, 2300, "نموذج_العملاء")
    Call انشاء_زر_تنقل(frm, "btn_المواد_الخام", "المواد الخام", 4100, 2300, "نموذج_المواد_الخام")
    Call انشاء_زر_تنقل(frm, "btn_المنتجات", "المنتجات التامة", 5300, 2300, "نموذج_المنتجات_التامة")
    Call انشاء_زر_تنقل(frm, "btn_المخازن", "المخازن", 500, 2800, "نموذج_المخازن")
    Call انشاء_زر_تنقل(frm, "btn_انواع_التكاليف", "أنواع التكاليف", 1700, 2800, "نموذج_انواع_التكاليف")
    
    ' قسم المشتريات والمخزون
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7500, 1800, 6000, 400)
    ctl.Caption = "المشتريات والمخزون"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(200, 255, 200)
    ctl.TextAlign = 2
    
    ' أزرار المشتريات والمخزون
    Call انشاء_زر_تنقل(frm, "btn_فواتير_المشتريات", "فواتير المشتريات", 7500, 2300, "نموذج_فواتير_المشتريات")
    Call انشاء_زر_تنقل(frm, "btn_ارصدة_المواد", "أرصدة المواد الخام", 9000, 2300, "نموذج_ارصدة_المواد_الخام")
    Call انشاء_زر_تنقل(frm, "btn_ارصدة_المنتجات", "أرصدة المنتجات", 10500, 2300, "نموذج_ارصدة_المنتجات_التامة")
    Call انشاء_زر_تنقل(frm, "btn_حركات_المخزون", "حركات المخزون", 7500, 2800, "نموذج_حركات_المخزون")
    
    ' قسم الإنتاج والوصفات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3600, 6000, 400)
    ctl.Caption = "الإنتاج والوصفات"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 230, 200)
    ctl.TextAlign = 2
    
    ' أزرار الإنتاج والوصفات
    Call انشاء_زر_تنقل(frm, "btn_وصفات_المنتجات", "وصفات المنتجات", 500, 4100, "نموذج_وصفات_المنتجات")
    Call انشاء_زر_تنقل(frm, "btn_اوامر_الانتاج", "أوامر الإنتاج", 2000, 4100, "نموذج_اوامر_الانتاج")
    Call انشاء_زر_تنقل(frm, "btn_تكاليف_الانتاج", "تكاليف الإنتاج", 3500, 4100, "عرض_تكاليف_اوامر_الانتاج")
    
    ' قسم المبيعات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7500, 3600, 6000, 400)
    ctl.Caption = "المبيعات"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 200, 255)
    ctl.TextAlign = 2
    
    ' أزرار المبيعات
    Call انشاء_زر_تنقل(frm, "btn_فواتير_المبيعات", "فواتير المبيعات", 7500, 4100, "نموذج_فواتير_المبيعات")
    Call انشاء_زر_تنقل(frm, "btn_تقارير_المبيعات", "تقارير المبيعات", 9000, 4100, "تقرير_المبيعات")
    
    ' قسم التقارير
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 5400, 13000, 400)
    ctl.Caption = "التقارير المحاسبية"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 255, 200)
    ctl.TextAlign = 2
    
    ' أزرار التقارير
    Call انشاء_زر_تنقل(frm, "btn_تقرير_ارصدة_المواد", "تقرير أرصدة المواد", 500, 5900, "تقرير_ارصدة_المواد_الخام")
    Call انشاء_زر_تنقل(frm, "btn_تقرير_ارصدة_المنتجات", "تقرير أرصدة المنتجات", 2200, 5900, "تقرير_ارصدة_المنتجات_التامة")
    Call انشاء_زر_تنقل(frm, "btn_تقرير_تكاليف_الانتاج", "تقرير تكاليف الإنتاج", 3900, 5900, "تقرير_تكاليف_الانتاج")
    Call انشاء_زر_تنقل(frm, "btn_تقرير_المشتريات", "تقرير المشتريات", 5600, 5900, "تقرير_المشتريات")
    Call انشاء_زر_تنقل(frm, "btn_تقرير_المبيعات", "تقرير المبيعات", 7300, 5900, "تقرير_المبيعات")
    Call انشاء_زر_تنقل(frm, "btn_تقرير_الربحية", "تقرير الربحية", 9000, 5900, "تقرير_الربحية")
    
    ' قسم الأدوات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 6800, 13000, 400)
    ctl.Caption = "أدوات النظام"
    ctl.FontSize = 16
    ctl.FontBold = True
    ctl.BackColor = RGB(220, 220, 220)
    ctl.TextAlign = 2
    
    ' أزرار الأدوات
    Call انشاء_زر_ادوات(frm, "btn_نسخ_احتياطي", "نسخ احتياطي", 500, 7300)
    Call انشاء_زر_ادوات(frm, "btn_استعادة_نسخة", "استعادة نسخة", 1700, 7300)
    Call انشاء_زر_ادوات(frm, "btn_اعدادات_النظام", "إعدادات النظام", 2900, 7300)
    Call انشاء_زر_ادوات(frm, "btn_دليل_المستخدم", "دليل المستخدم", 4100, 7300)
    Call انشاء_زر_ادوات(frm, "btn_حول_النظام", "حول النظام", 5300, 7300)
    
    ' معلومات النظام في الأسفل
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 8200, 6000, 300)
    ctl.Caption = "نظام محاسبة التكاليف - الإصدار 1.0"
    ctl.FontSize = 10
    ctl.ForeColor = RGB(100, 100, 100)
    
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7000, 8200, 6000, 300)
    ctl.Caption = "تاريخ اليوم: " & Format(Date, "dd/mm/yyyy")
    ctl.FontSize = 10
    ctl.ForeColor = RGB(100, 100, 100)
    ctl.TextAlign = 3 ' Right
    
    ' زر الخروج
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 11500, 8600, 1500, 400)
    ctl.Caption = "خروج"
    ctl.Name = "btn_خروج"
    ctl.FontSize = 12
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 100, 100)
    ctl.ForeColor = RGB(255, 255, 255)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج لوحة المعلومات
Private Sub انشاء_نموذج_لوحة_المعلومات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "لوحة_المعلومات"
    frm.Caption = "لوحة المعلومات"
    frm.DefaultView = 0
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = False
    frm.RecordSelectors = False
    
    ' عنوان لوحة المعلومات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 12000, 600)
    ctl.Caption = "لوحة المعلومات - ملخص النشاط"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' مربعات المعلومات السريعة
    Call انشاء_مربع_معلومات(frm, "عدد المواد الخام", "SELECT COUNT(*) FROM المواد_الخام WHERE حالة_المادة = 'نشط'", 500, 1000)
    Call انشاء_مربع_معلومات(frm, "عدد المنتجات التامة", "SELECT COUNT(*) FROM المنتجات_التامة WHERE حالة_المنتج = 'نشط'", 3000, 1000)
    Call انشاء_مربع_معلومات(frm, "أوامر الإنتاج النشطة", "SELECT COUNT(*) FROM اوامر_الانتاج WHERE حالة_الامر IN ('جديد', 'قيد التنفيذ')", 5500, 1000)
    Call انشاء_مربع_معلومات(frm, "إجمالي قيمة المخزون", "SELECT SUM(القيمة_الاجمالية) FROM ارصدة_المواد_الخام", 8000, 1000)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إجراء إنشاء زر تنقل
Private Sub انشاء_زر_تنقل(frm As Form, btnName As String, btnCaption As String, xPos As Long, yPos As Long, targetForm As String)
    Dim ctl As Control
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , xPos, yPos, 1400, 400)
    ctl.Caption = btnCaption
    ctl.Name = btnName
    ctl.FontSize = 10
    ctl.FontBold = True
    ctl.BackColor = RGB(240, 240, 240)
    ctl.BorderStyle = 1
    
    ' إضافة كود VBA للزر (سيتم إضافته لاحقاً)
    ctl.OnClick = "=فتح_نموذج(""" & targetForm & """)"
End Sub

' إجراء إنشاء زر أدوات
Private Sub انشاء_زر_ادوات(frm As Form, btnName As String, btnCaption As String, xPos As Long, yPos As Long)
    Dim ctl As Control
    
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , xPos, yPos, 1400, 400)
    ctl.Caption = btnCaption
    ctl.Name = btnName
    ctl.FontSize = 10
    ctl.BackColor = RGB(200, 200, 200)
    ctl.BorderStyle = 1
End Sub

' إجراء إنشاء مربع معلومات
Private Sub انشاء_مربع_معلومات(frm As Form, title As String, query As String, xPos As Long, yPos As Long)
    Dim ctl As Control
    
    ' عنوان المربع
    Set ctl = CreateControl(frm.Name, acLabel, , , , xPos, yPos, 2000, 300)
    ctl.Caption = title
    ctl.FontSize = 12
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(230, 230, 230)
    ctl.BorderStyle = 1
    
    ' قيمة المربع
    Set ctl = CreateControl(frm.Name, acTextBox, , , , xPos, yPos + 350, 2000, 600)
    ctl.ControlSource = "=DLookup(""*"", ""(" & query & ") AS T"")"
    ctl.FontSize = 24
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(255, 255, 255)
    ctl.BorderStyle = 1
    ctl.Enabled = False
End Sub

' إجراء فتح النماذج
Public Function فتح_نموذج(formName As String)
    On Error GoTo ErrorHandler
    
    DoCmd.OpenForm formName, acNormal
    Exit Function
    
ErrorHandler:
    MsgBox "لا يمكن فتح النموذج: " & formName & vbCrLf & "تأكد من وجود النموذج في قاعدة البيانات.", vbExclamation, "خطأ"
End Function
