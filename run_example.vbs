Option Explicit

Dim objExcel, objWorkbook, objFSO
Dim strCurrentPath, strExcelFile

Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentPath = objFSO.GetAbsolutePathName(".")
strExcelFile = strCurrentPath & "\نظام_محاسبة_التكاليف_متكامل.xlsx"

WScript.Echo "Starting practical example..."
WScript.Echo "Path: " & strCurrentPath

If Not objFSO.FileExists(strExcelFile) Then
    WScript.Echo "Error: Excel file not found!"
    WScript.Echo "Please run انشاء_نظام_بسيط.vbs first"
    WScript.Quit 1
End If

Set objExcel = CreateObject("Excel.Application")
objExcel.Visible = True
objExcel.DisplayAlerts = False

On Error Resume Next
Set objWorkbook = objExcel.Workbooks.Open(strExcelFile)

If Err.Number <> 0 Then
    WScript.Echo "Error opening Excel file: " & Err.Description
    objExcel.Quit
    WScript.Quit 1
End If

WScript.Echo "Excel file opened successfully"
WScript.Echo "System is ready for practical example"
WScript.Echo "Please manually import and run the VBA macros from:"
WScript.Echo "- مثال_عملي_واقعي.vba"
WScript.Echo "- اختبار_النظام.vba"

Set objWorkbook = Nothing
Set objExcel = Nothing
Set objFSO = Nothing
