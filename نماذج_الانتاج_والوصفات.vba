' كود VBA لإنشاء نماذج الإنتاج والوصفات
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء نماذج الإنتاج والوصفات
Public Sub انشاء_نماذج_الانتاج_والوصفات()
    On Error GoTo ErrorHandler
    
    ' إنشاء نماذج الوصفات
    Call انشاء_نموذج_وصفات_المنتجات
    Call انشاء_نموذج_تفاصيل_الوصفات
    
    ' إنشاء نماذج الإنتاج
    Call انشاء_نموذج_اوامر_الانتاج
    Call انشاء_نموذج_تكاليف_اوامر_الانتاج
    
    MsgBox "تم إنشاء نماذج الإنتاج والوصفات بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النماذج: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء نموذج وصفات المنتجات
Private Sub انشاء_نموذج_وصفات_المنتجات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_وصفات_المنتجات"
    frm.Caption = "وصفات المنتجات"
    frm.RecordSource = "وصفات_المنتجات"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 10000, 600)
    ctl.Caption = "وصفات المنتجات"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' كود الوصفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "كود الوصفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "كود_الوصفة"
    ctl.Enabled = False
    
    ' المنتج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "المنتج:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1400, 3500, 300)
    ctl.ControlSource = "كود_المنتج"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_المنتج, اسم_المنتج FROM المنتجات_التامة WHERE حالة_المنتج = 'نشط' ORDER BY اسم_المنتج"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3500"
    ctl.BoundColumn = 1
    
    ' اسم الوصفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "اسم الوصفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1800, 3500, 300)
    ctl.ControlSource = "اسم_الوصفة"
    
    ' كمية الإنتاج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6200, 1000, 1800, 300)
    ctl.Caption = "كمية الإنتاج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 8100, 1000, 1200, 300)
    ctl.ControlSource = "كمية_الانتاج"
    ctl.Format = "Standard"
    
    ' تاريخ الإضافة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6200, 1400, 1800, 300)
    ctl.Caption = "تاريخ الإضافة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 8100, 1400, 1200, 300)
    ctl.ControlSource = "تاريخ_الاضافة"
    ctl.Enabled = False
    
    ' حالة الوصفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6200, 1800, 1800, 300)
    ctl.Caption = "حالة الوصفة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 8100, 1800, 1200, 300)
    ctl.ControlSource = "حالة_الوصفة"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نشط;غير نشط;تحت التطوير"
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2200, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2200, 6000, 600)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' نموذج فرعي لتفاصيل الوصفة
    Set ctl = CreateControl(frm.Name, acSubform, , , , 500, 3000, 8500, 3000)
    ctl.SourceObject = "نموذج_تفاصيل_الوصفات"
    ctl.LinkChildFields = "كود_الوصفة"
    ctl.LinkMasterFields = "كود_الوصفة"
    ctl.Name = "subform_تفاصيل_الوصفة"
    
    ' أزرار التحكم
    Call اضافة_ازرار_الوصفات(frm, 6200)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج تفاصيل الوصفات
Private Sub انشاء_نموذج_تفاصيل_الوصفات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_تفاصيل_الوصفات"
    frm.Caption = "تفاصيل الوصفات"
    frm.RecordSource = "تفاصيل_الوصفات"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = False
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج أوامر الإنتاج
Private Sub انشاء_نموذج_اوامر_الانتاج()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_اوامر_الانتاج"
    frm.Caption = "أوامر الإنتاج"
    frm.RecordSource = "اوامر_الانتاج"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 12000, 600)
    ctl.Caption = "أوامر الإنتاج"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' رقم أمر الإنتاج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1800, 300)
    ctl.Caption = "رقم أمر الإنتاج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 1000, 1200, 300)
    ctl.ControlSource = "رقم_امر_الانتاج"
    ctl.Enabled = False
    
    ' المنتج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1800, 300)
    ctl.Caption = "المنتج:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1400, 3000, 300)
    ctl.ControlSource = "كود_المنتج"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_المنتج, اسم_المنتج FROM المنتجات_التامة WHERE حالة_المنتج = 'نشط' ORDER BY اسم_المنتج"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3000"
    ctl.BoundColumn = 1
    
    ' الوصفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1800, 300)
    ctl.Caption = "الوصفة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2400, 1800, 3000, 300)
    ctl.ControlSource = "كود_الوصفة"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_الوصفة, اسم_الوصفة FROM وصفات_المنتجات WHERE حالة_الوصفة = 'نشط' ORDER BY اسم_الوصفة"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3000"
    ctl.BoundColumn = 1
    
    ' كمية الإنتاج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6000, 1000, 1500, 300)
    ctl.Caption = "كمية الإنتاج:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7600, 1000, 1200, 300)
    ctl.ControlSource = "كمية_الانتاج"
    ctl.Format = "Standard"
    
    ' تاريخ الأمر
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6000, 1400, 1500, 300)
    ctl.Caption = "تاريخ الأمر:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7600, 1400, 1200, 300)
    ctl.ControlSource = "تاريخ_الامر"
    ctl.Format = "Short Date"
    
    ' تاريخ البدء
    Set ctl = CreateControl(frm.Name, acLabel, , , , 9200, 1000, 1200, 300)
    ctl.Caption = "تاريخ البدء:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 10500, 1000, 1200, 300)
    ctl.ControlSource = "تاريخ_البدء"
    ctl.Format = "Short Date"
    
    ' تاريخ الانتهاء
    Set ctl = CreateControl(frm.Name, acLabel, , , , 9200, 1400, 1200, 300)
    ctl.Caption = "تاريخ الانتهاء:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 10500, 1400, 1200, 300)
    ctl.ControlSource = "تاريخ_الانتهاء"
    ctl.Format = "Short Date"
    
    ' حالة الأمر
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6000, 1800, 1500, 300)
    ctl.Caption = "حالة الأمر:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 7600, 1800, 1500, 300)
    ctl.ControlSource = "حالة_الامر"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "جديد;قيد التنفيذ;مكتمل;ملغي"
    
    ' المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 9400, 1800, 1000, 300)
    ctl.Caption = "المخزن:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 10500, 1800, 1200, 300)
    ctl.ControlSource = "كود_المخزن"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_المخزن, اسم_المخزن FROM المخازن WHERE نوع_المخزن = 'منتجات تامة' ORDER BY اسم_المخزن"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;1200"
    ctl.BoundColumn = 1
    
    ' قسم التكاليف
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2400, 8000, 400)
    ctl.Caption = "التكاليف"
    ctl.FontSize = 14
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(200, 200, 200)
    
    ' تكلفة المواد الخام
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2900, 1800, 300)
    ctl.Caption = "تكلفة المواد الخام:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 2900, 1500, 300)
    ctl.ControlSource = "تكلفة_المواد_الخام"
    ctl.Format = "Currency"
    ctl.Enabled = False
    
    ' تكلفة العمالة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4200, 2900, 1500, 300)
    ctl.Caption = "تكلفة العمالة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 5800, 2900, 1500, 300)
    ctl.ControlSource = "تكلفة_العمالة"
    ctl.Format = "Currency"
    
    ' التكاليف المباشرة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7600, 2900, 1500, 300)
    ctl.Caption = "التكاليف المباشرة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 9200, 2900, 1500, 300)
    ctl.ControlSource = "التكاليف_المباشرة"
    ctl.Format = "Currency"
    
    ' التكاليف غير المباشرة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3300, 1800, 300)
    ctl.Caption = "التكاليف غير المباشرة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3300, 1500, 300)
    ctl.ControlSource = "التكاليف_غير_المباشرة"
    ctl.Format = "Currency"
    
    ' إجمالي التكلفة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 4200, 3300, 1500, 300)
    ctl.Caption = "إجمالي التكلفة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 5800, 3300, 1500, 300)
    ctl.ControlSource = "اجمالي_التكلفة"
    ctl.Format = "Currency"
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 255, 200)
    
    ' تكلفة الوحدة (محسوبة)
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7600, 3300, 1500, 300)
    ctl.Caption = "تكلفة الوحدة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 9200, 3300, 1500, 300)
    ctl.ControlSource = "=[اجمالي_التكلفة]/[كمية_الانتاج]"
    ctl.Format = "Currency"
    ctl.Enabled = False
    ctl.FontBold = True
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3700, 1800, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2400, 3700, 6000, 600)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' نموذج فرعي للتكاليف الإضافية
    Set ctl = CreateControl(frm.Name, acSubform, , , , 500, 4500, 8500, 2000)
    ctl.SourceObject = "نموذج_تكاليف_اوامر_الانتاج"
    ctl.LinkChildFields = "رقم_امر_الانتاج"
    ctl.LinkMasterFields = "رقم_امر_الانتاج"
    ctl.Name = "subform_تكاليف_اضافية"
    
    ' أزرار التحكم
    Call اضافة_ازرار_اوامر_الانتاج(frm, 6700)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج تكاليف أوامر الإنتاج
Private Sub انشاء_نموذج_تكاليف_اوامر_الانتاج()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_تكاليف_اوامر_الانتاج"
    frm.Caption = "تكاليف أوامر الإنتاج"
    frm.RecordSource = "تكاليف_اوامر_الانتاج"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = False
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إجراء إضافة أزرار التحكم للوصفات
Private Sub اضافة_ازرار_الوصفات(frm As Form, yPosition As Long)
    Dim ctl As Control
    
    ' زر وصفة جديدة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 500, yPosition, 1500, 400)
    ctl.Caption = "وصفة جديدة"
    ctl.Name = "btn_وصفة_جديدة"
    
    ' زر حفظ الوصفة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 2100, yPosition, 1500, 400)
    ctl.Caption = "حفظ الوصفة"
    ctl.Name = "btn_حفظ_الوصفة"
    
    ' زر حساب التكلفة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3700, yPosition, 1500, 400)
    ctl.Caption = "حساب التكلفة"
    ctl.Name = "btn_حساب_التكلفة"
    
    ' زر إغلاق
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 5300, yPosition, 1200, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btn_اغلاق"
End Sub

' إجراء إضافة أزرار التحكم لأوامر الإنتاج
Private Sub اضافة_ازرار_اوامر_الانتاج(frm As Form, yPosition As Long)
    Dim ctl As Control
    
    ' زر أمر جديد
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 500, yPosition, 1300, 400)
    ctl.Caption = "أمر جديد"
    ctl.Name = "btn_امر_جديد"
    
    ' زر حفظ الأمر
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1900, yPosition, 1300, 400)
    ctl.Caption = "حفظ الأمر"
    ctl.Name = "btn_حفظ_الامر"
    
    ' زر بدء الإنتاج
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3300, yPosition, 1300, 400)
    ctl.Caption = "بدء الإنتاج"
    ctl.Name = "btn_بدء_الانتاج"
    
    ' زر إنهاء الإنتاج
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 4700, yPosition, 1300, 400)
    ctl.Caption = "إنهاء الإنتاج"
    ctl.Name = "btn_انهاء_الانتاج"
    
    ' زر طباعة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 6100, yPosition, 1000, 400)
    ctl.Caption = "طباعة"
    ctl.Name = "btn_طباعة"
    
    ' زر إغلاق
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 7200, yPosition, 1000, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btn_اغلاق"
End Sub
