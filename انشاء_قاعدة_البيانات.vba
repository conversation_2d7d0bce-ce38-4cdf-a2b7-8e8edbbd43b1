' كود VBA لإنشاء قاعدة بيانات نظام محاسبة التكاليف
' يتم تشغيل هذا الكود في Microsoft Access

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء جميع الجداول
Public Sub انشاء_قاعدة_البيانات()
    On Error GoTo ErrorHandler
    
    ' إنشاء الجداول الأساسية
    Call انشاء_جدول_الوحدات
    Call انشاء_جدول_الموردين
    Call انشاء_جدول_العملاء
    Call انشاء_جدول_المواد_الخام
    Call انشاء_جدول_المنتجات_التامة
    Call انشاء_جدول_المخازن
    
    ' إنشاء جداول المخزون
    Call انشاء_جدول_ارصدة_المواد_الخام
    Call انشاء_جدول_ارصدة_المنتجات_التامة
    Call انشاء_جدول_حركات_المخزون
    
    ' إنشاء جداول الوصفات والإنتاج
    Call انشاء_جدول_وصفات_المنتجات
    Call انشاء_جدول_تفاصيل_الوصفات
    Call انشاء_جدول_اوامر_الانتاج
    Call انشاء_جدول_تفاصيل_استهلاك_الانتاج
    
    ' إنشاء جداول المشتريات والمبيعات
    Call انشاء_جدول_فواتير_المشتريات
    Call انشاء_جدول_تفاصيل_فواتير_المشتريات
    Call انشاء_جدول_فواتير_المبيعات
    Call انشاء_جدول_تفاصيل_فواتير_المبيعات
    
    ' إنشاء جداول التكاليف
    Call انشاء_جدول_انواع_التكاليف
    Call انشاء_جدول_تكاليف_اوامر_الانتاج
    
    ' إدراج البيانات الأساسية
    Call ادراج_البيانات_الاساسية
    
    MsgBox "تم إنشاء قاعدة البيانات بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء قاعدة البيانات: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء جدول الوحدات
Private Sub انشاء_جدول_الوحدات()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("الوحدات")
    
    ' إضافة الحقول
    Set fld = tdf.CreateField("كود_الوحدة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_الوحدة", dbText, 50)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اختصار_الوحدة", dbText, 10)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' إضافة المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_الوحدة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول الموردين
Private Sub انشاء_جدول_الموردين()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("الموردين")
    
    Set fld = tdf.CreateField("كود_المورد", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_المورد", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("العنوان", dbText, 200)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الهاتف", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الجوال", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("البريد_الالكتروني", dbText, 100)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الرقم_الضريبي", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("حالة_المورد", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_المورد")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول العملاء
Private Sub انشاء_جدول_العملاء()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("العملاء")
    
    Set fld = tdf.CreateField("كود_العميل", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_العميل", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("العنوان", dbText, 200)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الهاتف", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الجوال", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("البريد_الالكتروني", dbText, 100)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الرقم_الضريبي", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("حالة_العميل", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_العميل")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول المواد الخام
Private Sub انشاء_جدول_المواد_الخام()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("المواد_الخام")
    
    Set fld = tdf.CreateField("كود_المادة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_المادة", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("كود_الوحدة", dbLong)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الحد_الادنى", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الحد_الاقصى", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("متوسط_التكلفة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("حالة_المادة", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_المادة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول المنتجات التامة
Private Sub انشاء_جدول_المنتجات_التامة()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("المنتجات_التامة")
    
    Set fld = tdf.CreateField("كود_المنتج", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_المنتج", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("كود_الوحدة", dbLong)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("سعر_البيع", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تكلفة_الانتاج", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الحد_الادنى", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("الحد_الاقصى", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("حالة_المنتج", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_المنتج")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول أرصدة المواد الخام
Private Sub انشاء_جدول_ارصدة_المواد_الخام()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("ارصدة_المواد_الخام")

    Set fld = tdf.CreateField("كود_الرصيد", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المادة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية_المتاحة", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("متوسط_التكلفة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_اخر_تحديث", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_الرصيد")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول أرصدة المنتجات التامة
Private Sub انشاء_جدول_ارصدة_المنتجات_التامة()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("ارصدة_المنتجات_التامة")

    Set fld = tdf.CreateField("كود_الرصيد", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المنتج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية_المتاحة", dbDouble)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("متوسط_التكلفة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_اخر_تحديث", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_الرصيد")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول حركات المخزون
Private Sub انشاء_جدول_حركات_المخزون()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("حركات_المخزون")

    Set fld = tdf.CreateField("كود_الحركة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("نوع_الحركة", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("نوع_المادة", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المادة", dbLong)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("سعر_الوحدة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_المستند", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("نوع_المستند", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الحركة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_الحركة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول وصفات المنتجات
Private Sub انشاء_جدول_وصفات_المنتجات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("وصفات_المنتجات")

    Set fld = tdf.CreateField("كود_الوصفة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المنتج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اسم_الوصفة", dbText, 100)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كمية_الانتاج", dbDouble)
    fld.DefaultValue = "1"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("حالة_الوصفة", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_الوصفة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول تفاصيل الوصفات
Private Sub انشاء_جدول_تفاصيل_الوصفات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("تفاصيل_الوصفات")

    Set fld = tdf.CreateField("كود_التفصيل", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_الوصفة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المادة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية_المطلوبة", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_التفصيل")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول أوامر الإنتاج
Private Sub انشاء_جدول_اوامر_الانتاج()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("اوامر_الانتاج")

    Set fld = tdf.CreateField("رقم_امر_الانتاج", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المنتج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_الوصفة", dbLong)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كمية_الانتاج", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الامر", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_البدء", dbDate)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الانتهاء", dbDate)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("حالة_الامر", dbText, 50)
    fld.DefaultValue = """جديد"""
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تكلفة_المواد_الخام", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تكلفة_العمالة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("التكاليف_المباشرة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("التكاليف_غير_المباشرة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اجمالي_التكلفة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("رقم_امر_الانتاج")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول تفاصيل استهلاك الإنتاج
Private Sub انشاء_جدول_تفاصيل_استهلاك_الانتاج()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("تفاصيل_استهلاك_الانتاج")

    Set fld = tdf.CreateField("كود_التفصيل", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_امر_الانتاج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المادة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية_المستهلكة", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("سعر_الوحدة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_التفصيل")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول فواتير المشتريات
Private Sub انشاء_جدول_فواتير_المشتريات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("فواتير_المشتريات")

    Set fld = tdf.CreateField("رقم_الفاتورة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المورد", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الفاتورة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_فاتورة_المورد", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اجمالي_الفاتورة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الخصم", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الضريبة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("صافي_الفاتورة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("حالة_الفاتورة", dbText, 50)
    fld.DefaultValue = """مؤكدة"""
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("رقم_الفاتورة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول تفاصيل فواتير المشتريات
Private Sub انشاء_جدول_تفاصيل_فواتير_المشتريات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("تفاصيل_فواتير_المشتريات")

    Set fld = tdf.CreateField("كود_التفصيل", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_الفاتورة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المادة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("سعر_الوحدة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_التفصيل")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول فواتير المبيعات
Private Sub انشاء_جدول_فواتير_المبيعات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("فواتير_المبيعات")

    Set fld = tdf.CreateField("رقم_الفاتورة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_العميل", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_الفاتورة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اجمالي_الفاتورة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الخصم", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الضريبة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("صافي_الفاتورة", dbCurrency)
    fld.DefaultValue = "0"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("حالة_الفاتورة", dbText, 50)
    fld.DefaultValue = """مؤكدة"""
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("رقم_الفاتورة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول تفاصيل فواتير المبيعات
Private Sub انشاء_جدول_تفاصيل_فواتير_المبيعات()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("تفاصيل_فواتير_المبيعات")

    Set fld = tdf.CreateField("كود_التفصيل", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_الفاتورة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المنتج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("الكمية", dbDouble)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("سعر_الوحدة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("القيمة_الاجمالية", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تكلفة_الوحدة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اجمالي_التكلفة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_التفصيل")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول أنواع التكاليف
Private Sub انشاء_جدول_انواع_التكاليف()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("انواع_التكاليف")

    Set fld = tdf.CreateField("كود_نوع_التكلفة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("اسم_نوع_التكلفة", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تصنيف_التكلفة", dbText, 50)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("حالة_النوع", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_نوع_التكلفة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إنشاء جدول تكاليف أوامر الإنتاج
Private Sub انشاء_جدول_تكاليف_اوامر_الانتاج()
    Dim tdf As TableDef
    Dim fld As Field

    Set tdf = CurrentDb.CreateTableDef("تكاليف_اوامر_الانتاج")

    Set fld = tdf.CreateField("كود_التكلفة", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("رقم_امر_الانتاج", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("كود_نوع_التكلفة", dbLong)
    fld.Required = True
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("قيمة_التكلفة", dbCurrency)
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("تاريخ_التكلفة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld

    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld

    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_التكلفة")
    idx.Fields.Append fld
    tdf.Indexes.Append idx

    CurrentDb.TableDefs.Append tdf
End Sub

' إجراء إدراج البيانات الأساسية
Private Sub ادراج_البيانات_الاساسية()
    On Error GoTo ErrorHandler

    ' إدراج الوحدات الأساسية
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('كيلوجرام', 'كجم', 'وحدة الوزن الأساسية')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('جرام', 'جم', 'وحدة وزن صغيرة')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('لتر', 'لتر', 'وحدة الحجم للسوائل')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('مليلتر', 'مل', 'وحدة حجم صغيرة')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('قطعة', 'قطعة', 'وحدة العدد')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('علبة', 'علبة', 'وحدة التعبئة')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('كيس', 'كيس', 'وحدة التعبئة')"
    CurrentDb.Execute "INSERT INTO الوحدات (اسم_الوحدة, اختصار_الوحدة, ملاحظات) VALUES ('صندوق', 'صندوق', 'وحدة التعبئة الكبيرة')"

    ' إدراج أنواع التكاليف الأساسية
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('أجور العمالة المباشرة', 'عمالة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('أجور العمالة غير المباشرة', 'عمالة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('استهلاك الآلات', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('استهلاك المباني', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('الكهرباء', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('المياه', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('الغاز', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('صيانة الآلات', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('مواد التنظيف', 'غير مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('مواد التعبئة والتغليف', 'مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('النقل والشحن', 'مباشرة')"
    CurrentDb.Execute "INSERT INTO انواع_التكاليف (اسم_نوع_التكلفة, تصنيف_التكلفة) VALUES ('الرقابة والجودة', 'غير مباشرة')"

    ' إدراج المخازن الأساسية
    CurrentDb.Execute "INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, ملاحظات) VALUES ('مخزن المواد الخام الرئيسي', 'مواد خام', 'المخزن الرئيسي للمواد الخام')"
    CurrentDb.Execute "INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, ملاحظات) VALUES ('مخزن المواد الخام الثانوي', 'مواد خام', 'مخزن احتياطي للمواد الخام')"
    CurrentDb.Execute "INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, ملاحظات) VALUES ('مخزن المنتجات التامة', 'منتجات تامة', 'مخزن المنتجات الجاهزة للبيع')"
    CurrentDb.Execute "INSERT INTO المخازن (اسم_المخزن, نوع_المخزن, ملاحظات) VALUES ('مخزن المنتجات قيد التصنيع', 'منتجات تامة', 'مخزن المنتجات قيد الإنتاج')"

    Exit Sub

ErrorHandler:
    MsgBox "حدث خطأ أثناء إدراج البيانات الأساسية: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء جدول المخازن
Private Sub انشاء_جدول_المخازن()
    Dim tdf As TableDef
    Dim fld As Field
    
    Set tdf = CurrentDb.CreateTableDef("المخازن")
    
    Set fld = tdf.CreateField("كود_المخزن", dbLong)
    fld.Attributes = dbAutoIncrField
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("اسم_المخزن", dbText, 100)
    fld.Required = True
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("نوع_المخزن", dbText, 50)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("العنوان", dbText, 200)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("المسؤول", dbText, 100)
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("حالة_المخزن", dbText, 20)
    fld.DefaultValue = """نشط"""
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("تاريخ_الاضافة", dbDate)
    fld.DefaultValue = "Now()"
    tdf.Fields.Append fld
    
    Set fld = tdf.CreateField("ملاحظات", dbMemo)
    tdf.Fields.Append fld
    
    ' المفتاح الأساسي
    Dim idx As Index
    Set idx = tdf.CreateIndex("PrimaryKey")
    idx.Primary = True
    idx.Required = True
    Set fld = idx.CreateField("كود_المخزن")
    idx.Fields.Append fld
    tdf.Indexes.Append idx
    
    CurrentDb.TableDefs.Append tdf
End Sub
