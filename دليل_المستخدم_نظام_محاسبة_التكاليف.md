# دليل المستخدم - نظام محاسبة التكاليف لمصنع المواد الغذائية

## نظرة عامة على النظام

هذا النظام مصمم خصيصاً لإدارة محاسبة التكاليف في مصانع المواد الغذائية، ويشمل:
- إدارة المشتريات من الموردين
- إدارة المخازن والمخزون
- إدارة وصفات المنتجات
- إدارة أوامر الإنتاج وحساب التكاليف
- إدارة المبيعات للعملاء
- تقارير محاسبية شاملة

## خطوات تشغيل النظام

### 1. إنشاء قاعدة البيانات

1. افتح Microsoft Access
2. أنشئ قاعدة بيانات جديدة باسم "نظام_محاسبة_التكاليف"
3. اذهب إلى علامة التبويب "إنشاء" ← "وحدة نمطية"
4. انسخ والصق كود VBA من ملف `انشاء_قاعدة_البيانات.vba`
5. احفظ الوحدة النمطية باسم "انشاء_قاعدة_البيانات"
6. اضغط F5 لتشغيل الإجراء `انشاء_قاعدة_البيانات()`

### 2. التحقق من إنشاء الجداول

بعد تشغيل الكود، تأكد من وجود الجداول التالية:

#### الجداول الأساسية:
- **الوحدات**: وحدات القياس (كجم، جرام، لتر، إلخ)
- **الموردين**: بيانات الموردين
- **العملاء**: بيانات العملاء
- **المواد_الخام**: المواد الخام المستخدمة في الإنتاج
- **المنتجات_التامة**: المنتجات النهائية
- **المخازن**: المخازن المختلفة

#### جداول المخزون:
- **ارصدة_المواد_الخام**: أرصدة المواد الخام الحالية
- **ارصدة_المنتجات_التامة**: أرصدة المنتجات التامة
- **حركات_المخزون**: سجل جميع حركات المخزون

#### جداول الإنتاج:
- **وصفات_المنتجات**: وصفات تصنيع المنتجات
- **تفاصيل_الوصفات**: المواد الخام المطلوبة لكل وصفة
- **اوامر_الانتاج**: أوامر الإنتاج
- **تفاصيل_استهلاك_الانتاج**: استهلاك المواد الخام في الإنتاج

#### جداول المشتريات والمبيعات:
- **فواتير_المشتريات**: فواتير شراء المواد الخام
- **تفاصيل_فواتير_المشتريات**: تفاصيل كل فاتورة شراء
- **فواتير_المبيعات**: فواتير بيع المنتجات
- **تفاصيل_فواتير_المبيعات**: تفاصيل كل فاتورة بيع

#### جداول التكاليف:
- **انواع_التكاليف**: أنواع التكاليف المختلفة
- **تكاليف_اوامر_الانتاج**: التكاليف الإضافية لأوامر الإنتاج

## سير العمل في النظام

### 1. إعداد البيانات الأساسية

#### أ. إدخال الموردين:
```
الحقول المطلوبة:
- اسم المورد (مطلوب)
- العنوان
- الهاتف
- الجوال
- البريد الإلكتروني
- الرقم الضريبي
```

#### ب. إدخال العملاء:
```
الحقول المطلوبة:
- اسم العميل (مطلوب)
- العنوان
- الهاتف
- الجوال
- البريد الإلكتروني
- الرقم الضريبي
```

#### ج. إدخال المواد الخام:
```
الحقول المطلوبة:
- اسم المادة (مطلوب)
- وحدة القياس
- الحد الأدنى للمخزون
- الحد الأقصى للمخزون
```

#### د. إدخال المنتجات التامة:
```
الحقول المطلوبة:
- اسم المنتج (مطلوب)
- وحدة القياس
- سعر البيع
- الحد الأدنى للمخزون
- الحد الأقصى للمخزون
```

### 2. إدارة المشتريات

#### خطوات شراء المواد الخام:
1. إنشاء فاتورة شراء جديدة
2. اختيار المورد
3. إدخال تفاصيل المواد المشتراة:
   - المادة الخام
   - الكمية
   - سعر الوحدة
   - المخزن المستقبل
4. حفظ الفاتورة
5. **النظام سيقوم تلقائياً بـ:**
   - تحديث أرصدة المواد الخام
   - إضافة حركة وارد للمخزون
   - حساب متوسط التكلفة

### 3. إدارة الوصفات

#### إنشاء وصفة منتج:
1. اختيار المنتج
2. تحديد كمية الإنتاج للدفعة الواحدة
3. إضافة المواد الخام المطلوبة:
   - اختيار المادة الخام
   - تحديد الكمية المطلوبة
4. حفظ الوصفة

#### مثال: وصفة زيتون أخضر مشوي باللبنة (10 أكياس):
```
- زيتون أخضر: 3.5 كجم
- لبنة: 1.5 كجم
- زيت نباتي: 200 مل
- ملح: 50 جرام
```

### 4. إدارة الإنتاج

#### إنشاء أمر إنتاج:
1. اختيار المنتج المراد إنتاجه
2. اختيار الوصفة
3. تحديد كمية الإنتاج
4. اختيار المخزن المستقبل للمنتج التام
5. **النظام سيقوم بـ:**
   - حساب المواد الخام المطلوبة
   - التحقق من توفر المواد في المخزون
   - حساب تكلفة المواد الخام

#### إضافة التكاليف الإضافية:
1. تكلفة العمالة المباشرة
2. تكلفة العمالة غير المباشرة
3. التكاليف المباشرة (تعبئة، نقل)
4. التكاليف غير المباشرة (كهرباء، مياه، استهلاك)

#### إنهاء أمر الإنتاج:
عند إنهاء الإنتاج، النظام سيقوم بـ:
- صرف المواد الخام من المخزون
- إضافة المنتج التام للمخزون
- حساب تكلفة الوحدة النهائية
- تحديث جميع الأرصدة

### 5. إدارة المبيعات

#### إنشاء فاتورة بيع:
1. اختيار العميل
2. إضافة المنتجات:
   - اختيار المنتج
   - تحديد الكمية
   - سعر الوحدة
   - المخزن المصدر
3. حساب الخصومات والضرائب
4. حفظ الفاتورة
5. **النظام سيقوم بـ:**
   - صرف المنتجات من المخزون
   - تحديث أرصدة المنتجات التامة
   - حساب تكلفة البضاعة المباعة

## التقارير المحاسبية

### 1. تقارير المخزون:
- أرصدة المواد الخام الحالية
- أرصدة المنتجات التامة
- حركات المخزون (وارد/صادر)
- المواد التي وصلت للحد الأدنى

### 2. تقارير التكاليف:
- تكلفة أوامر الإنتاج
- تحليل التكاليف حسب النوع
- تكلفة الوحدة لكل منتج
- مقارنة التكاليف الفعلية بالمعيارية

### 3. تقارير المبيعات:
- فواتير المبيعات
- تحليل المبيعات حسب المنتج
- تحليل المبيعات حسب العميل
- هامش الربح لكل منتج

### 4. تقارير المشتريات:
- فواتير المشتريات
- تحليل المشتريات حسب المورد
- تحليل المشتريات حسب المادة
- متوسط أسعار المواد الخام

## نصائح مهمة

### 1. إدارة المخزون:
- تأكد من إدخال جميع حركات المخزون
- راجع الأرصدة بانتظام
- اعتمد على تقارير النظام لاتخاذ القرارات

### 2. حساب التكاليف:
- أدخل جميع التكاليف الإضافية بدقة
- راجع وصفات المنتجات بانتظام
- احسب متوسط التكلفة بناءً على البيانات الفعلية

### 3. الرقابة والمتابعة:
- راجع تقارير التكاليف شهرياً
- قارن التكاليف الفعلية بالمخططة
- تابع هوامش الربح لكل منتج

### 4. النسخ الاحتياطي:
- اعمل نسخة احتياطية من قاعدة البيانات يومياً
- احتفظ بنسخ متعددة في أماكن مختلفة
- اختبر استعادة النسخ الاحتياطية بانتظام

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في حساب الأرصدة:
- تأكد من إدخال جميع الحركات
- راجع تواريخ الحركات
- تحقق من صحة الكميات والأسعار

#### 2. عدم توفر مواد خام للإنتاج:
- راجع أرصدة المواد الخام
- تأكد من صحة الوصفات
- اطلب المواد الناقصة من الموردين

#### 3. اختلاف في تكلفة المنتجات:
- راجع أسعار المواد الخام
- تحقق من التكاليف الإضافية
- تأكد من صحة الوصفات والكميات

---

**ملاحظة**: هذا النظام مصمم ليكون مرناً وقابلاً للتطوير. يمكن إضافة المزيد من الميزات حسب احتياجات المصنع.
