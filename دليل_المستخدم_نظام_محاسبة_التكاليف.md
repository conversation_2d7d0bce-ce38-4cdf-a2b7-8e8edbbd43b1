# دليل المستخدم - نظام محاسبة التكاليف المتكامل

## نظرة عامة على النظام

نظام محاسبة التكاليف المتكامل هو حل شامل مصمم خصيصاً لمصانع المواد الغذائية باستخدام Microsoft Excel. يدعم النظام إنتاج منتجات مثل:
- الزيتون الأخضر المشوي باللبنة
- المعمول بالجوز
- المعمول بعين الجمل

## مكونات النظام

### 1. لوحة التحكم الرئيسية
الورقة الأولى في النظام تحتوي على:
- عنوان النظام ومعلومات المصنع
- أزرار التنقل السريع للأقسام المختلفة
- معلومات المنتجات المدعومة

### 2. البيانات الأساسية

#### أ. جدول الموردين
- **الغرض**: إدارة بيانات الموردين
- **الحقول**:
  - كود المورد (SUP001, SUP002, ...)
  - اسم المورد
  - العنوان
  - الهاتف
  - البريد الإلكتروني
  - ملاحظات

#### ب. جدول العملاء
- **الغرض**: إدارة بيانات العملاء
- **الحقول**:
  - كود العميل (CUS001, CUS002, ...)
  - اسم العميل
  - العنوان
  - الهاتف
  - البريد الإلكتروني
  - نوع العميل (تجزئة، مطاعم، جملة)

#### ج. جدول المواد الخام
- **الغرض**: تعريف المواد الخام المستخدمة في الإنتاج
- **الحقول**:
  - كود المادة (RM001, RM002, ...)
  - اسم المادة
  - وحدة القياس
  - السعر للوحدة
  - الحد الأدنى للمخزون
  - المورد الرئيسي
  - ملاحظات

#### د. جدول المنتجات التامة
- **الغرض**: تعريف المنتجات النهائية
- **الحقول**:
  - كود المنتج (FG001, FG002, ...)
  - اسم المنتج
  - وحدة القياس
  - سعر البيع
  - مدة الصلاحية
  - ملاحظات

#### هـ. جدول وحدات القياس
- **الغرض**: تعريف وحدات القياس المستخدمة
- **الحقول**:
  - كود الوحدة (KG, GM, PC, BOX, LT)
  - اسم الوحدة
  - ملاحظات

#### و. جدول مراكز التكلفة
- **الغرض**: تعريف مراكز التكلفة في المصنع
- **الحقول**:
  - كود المركز (CC001, CC002, ...)
  - اسم المركز
  - نوع المركز (إنتاج، خدمي، إداري)
  - ملاحظات

### 3. إدارة المشتريات والمخزون

#### أ. سجل المشتريات
- **الغرض**: تسجيل جميع عمليات الشراء
- **الحقول**:
  - رقم الفاتورة
  - التاريخ
  - كود المورد
  - كود المادة
  - الكمية
  - سعر الوحدة
  - إجمالي القيمة (محسوب تلقائياً)
  - ملاحظات

#### ب. مخزون المواد الخام
- **الغرض**: متابعة أرصدة المواد الخام
- **الحقول**:
  - كود المادة
  - اسم المادة
  - الرصيد الحالي
  - متوسط التكلفة
  - قيمة المخزون (محسوبة تلقائياً)
  - آخر تحديث
  - حالة المخزون (متوفر/منخفض)

### 4. إدارة الإنتاج

#### أ. جدول الوصفات
- **الغرض**: تحديد المواد الخام المطلوبة لكل منتج
- **الحقول**:
  - كود المنتج
  - كود المادة الخام
  - اسم المادة
  - الكمية المطلوبة
  - وحدة القياس
  - ملاحظات

**مثال على وصفة الزيتون الأخضر المشوي باللبنة:**
- زيتون أخضر: 0.3 كيلوغرام
- لبنة طبيعية: 0.2 كيلوغرام

#### ب. أوامر الإنتاج
- **الغرض**: تسجيل أوامر الإنتاج وحساب التكاليف
- **الحقول**:
  - رقم الأمر
  - التاريخ
  - كود المنتج
  - اسم المنتج
  - الكمية المطلوبة
  - تكلفة المواد الخام
  - تكلفة العمالة
  - التكاليف الإضافية
  - إجمالي التكلفة (محسوب تلقائياً)
  - الحالة

#### ج. مخزون المنتجات التامة
- **الغرض**: متابعة أرصدة المنتجات النهائية
- **الحقول**:
  - كود المنتج
  - اسم المنتج
  - الرصيد الحالي
  - تكلفة الوحدة
  - قيمة المخزون (محسوبة تلقائياً)
  - سعر البيع
  - آخر تحديث
  - حالة المخزون

### 5. إدارة المبيعات

#### سجل المبيعات
- **الغرض**: تسجيل جميع عمليات البيع
- **الحقول**:
  - رقم الفاتورة
  - التاريخ
  - كود العميل
  - كود المنتج
  - اسم المنتج
  - الكمية
  - سعر الوحدة
  - إجمالي المبيعات (محسوب تلقائياً)
  - ملاحظات

### 6. التقارير والمحاسبة

#### أ. تقرير التكاليف
- **الغرض**: حساب تكلفة الإنتاج لكل منتج
- **المحتوى**:
  - تكاليف المواد الخام
  - تكاليف العمالة المباشرة
  - التكاليف الصناعية غير المباشرة
  - إجمالي التكلفة لكل منتج

#### ب. تقرير المخزون الشامل
- **الغرض**: عرض أرصدة جميع المخازن
- **المحتوى**:
  - مخزون المواد الخام (الكمية والقيمة)
  - مخزون المنتجات التامة (الكمية والقيمة)
  - إجمالي قيمة المخزون
  - تحليل حالة المخزون

#### ج. التقارير المالية
- **الغرض**: التحليل المالي والمحاسبي
- **المحتوى**:
  - قائمة الأرباح والخسائر
  - تحليل الربحية بالمنتج
  - نسب التكاليف
  - مؤشرات الأداء المالي

## طريقة الاستخدام

### 1. البدء بالنظام
1. افتح ملف "نظام_محاسبة_التكاليف_متكامل.xlsx"
2. ابدأ من ورقة "لوحة_التحكم"
3. استخدم أزرار التنقل للانتقال بين الأقسام

### 2. إدخال البيانات الأساسية
1. ابدأ بإدخال بيانات الموردين في ورقة "الموردين"
2. أدخل بيانات العملاء في ورقة "العملاء"
3. أدخل بيانات المواد الخام في ورقة "المواد_الخام"
4. أدخل بيانات المنتجات في ورقة "المنتجات_التامة"

### 3. إعداد الوصفات
1. انتقل إلى ورقة "الوصفات"
2. أدخل المواد الخام المطلوبة لكل منتج
3. حدد الكميات المطلوبة بدقة

### 4. تسجيل المشتريات
1. انتقل إلى ورقة "المشتريات"
2. أدخل تفاصيل كل عملية شراء
3. سيتم حساب إجمالي القيمة تلقائياً
4. تحديث مخزون المواد الخام يدوياً

### 5. إنشاء أوامر الإنتاج
1. انتقل إلى ورقة "اوامر_الانتاج"
2. أدخل تفاصيل أمر الإنتاج
3. احسب تكاليف المواد الخام حسب الوصفة
4. أضف تكاليف العمالة والتكاليف الإضافية
5. سيتم حساب إجمالي التكلفة تلقائياً

### 6. تسجيل المبيعات
1. انتقل إلى ورقة "المبيعات"
2. أدخل تفاصيل كل عملية بيع
3. سيتم حساب إجمالي المبيعات تلقائياً
4. تحديث مخزون المنتجات التامة يدوياً

### 7. مراجعة التقارير
1. راجع "تقرير_التكاليف" لمتابعة تكاليف الإنتاج
2. راجع "تقرير_المخزون" لمتابعة أرصدة المخازن
3. راجع "التقارير_المالية" للتحليل المالي

## نصائح مهمة

### 1. إدارة البيانات
- تأكد من إدخال البيانات بدقة
- استخدم أكواد موحدة للمواد والمنتجات
- حدث البيانات بانتظام

### 2. حساب التكاليف
- راجع الوصفات بانتظام
- احسب تكاليف العمالة بدقة
- لا تنس التكاليف الإضافية

### 3. إدارة المخزون
- راقب الحد الأدنى للمخزون
- حدث الأرصدة بعد كل عملية
- راجع تقارير المخزون دورياً

### 4. التقارير المالية
- راجع التقارير شهرياً
- قارن الأرقام الفعلية بالمخططة
- استخدم التقارير في اتخاذ القرارات

## الدعم والصيانة

### 1. النسخ الاحتياطي
- احفظ نسخة احتياطية يومياً
- احتفظ بنسخ متعددة في أماكن مختلفة

### 2. التحديثات
- راجع الصيغ بانتظام
- تأكد من صحة الحسابات
- حدث البيانات الأساسية عند الحاجة

### 3. الأمان
- احم الملف بكلمة مرور
- قيد الوصول للمستخدمين المخولين فقط

---

**ملاحظة**: هذا النظام مصمم لتلبية احتياجات مصانع المواد الغذائية الصغيرة والمتوسطة. يمكن تخصيصه وتطويره حسب الاحتياجات الخاصة.
