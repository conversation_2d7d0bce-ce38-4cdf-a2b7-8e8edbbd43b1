' سكريبت VBScript لإنشاء نظام محاسبة التكاليف في Excel
' تم تطويره لمصنع المواد الغذائية

Dim xlApp, xlWorkbook, xlWorksheet
Dim filePath, i

' إنشاء كائن Excel
Set xlApp = CreateObject("Excel.Application")
xlApp.Visible = True
xlApp.DisplayAlerts = False

' إنشاء مصنف جديد
Set xlWorkbook = xlApp.Workbooks.Add

' تحديد مسار الحفظ
filePath = "C:\Users\<USER>\OneDrive\new\ابوفرح\new\نظام_محاسبة_التكاليف.xlsx"

' حذف الأوراق الافتراضية
xlApp.DisplayAlerts = False
For i = xlWorkbook.Worksheets.Count To 2 Step -1
    xlWorkbook.Worksheets(i).Delete
Next i
xlApp.DisplayAlerts = True

' إعادة تسمية الورقة الأولى
xlWorkbook.Worksheets(1).Name = "لوحة_التحكم"

' إضافة الأوراق المطلوبة
xlWorkbook.Worksheets.Add.Name = "البيانات_الاساسية"
xlWorkbook.Worksheets.Add.Name = "الموردين"
xlWorkbook.Worksheets.Add.Name = "العملاء"
xlWorkbook.Worksheets.Add.Name = "المواد_الخام"
xlWorkbook.Worksheets.Add.Name = "المنتجات_التامة"
xlWorkbook.Worksheets.Add.Name = "المشتريات"
xlWorkbook.Worksheets.Add.Name = "مخزون_المواد_الخام"
xlWorkbook.Worksheets.Add.Name = "الوصفات"
xlWorkbook.Worksheets.Add.Name = "اوامر_الانتاج"
xlWorkbook.Worksheets.Add.Name = "مخزون_المنتجات_التامة"
xlWorkbook.Worksheets.Add.Name = "المبيعات"
xlWorkbook.Worksheets.Add.Name = "تقرير_التكاليف"
xlWorkbook.Worksheets.Add.Name = "تقرير_المخزون"
xlWorkbook.Worksheets.Add.Name = "التقارير_المالية"

' ترتيب الأوراق
xlWorkbook.Worksheets("لوحة_التحكم").Move xlWorkbook.Worksheets(1)

' حفظ الملف
xlWorkbook.SaveAs filePath

WScript.Echo "تم إنشاء ملف Excel بنجاح في: " & filePath
WScript.Echo "يحتوي الملف على " & xlWorkbook.Worksheets.Count & " ورقة عمل"

' إبقاء Excel مفتوحاً للعمل
' xlApp.Quit
Set xlWorksheet = Nothing
Set xlWorkbook = Nothing
Set xlApp = Nothing
