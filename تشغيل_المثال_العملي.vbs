' سكريپت تشغيل المثال العملي الواقعي
' شركة أبو فرح للمواد الغذائية

Option Explicit

Dim objExcel, objWorkbook, objFSO
Dim strCurrentPath, strExcelFile

' الحصول على المسار الحالي
Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentPath = objFSO.GetAbsolutePathName(".")
strExcelFile = strCurrentPath & "\نظام_محاسبة_التكاليف_متكامل.xlsx"

WScript.Echo "بدء تشغيل المثال العملي الواقعي..."
WScript.Echo "المسار: " & strCurrentPath
WScript.Echo "ملف Excel: " & strExcelFile

' التحقق من وجود ملف Excel
If Not objFSO.FileExists(strExcelFile) Then
    WScript.Echo "خطأ: ملف Excel غير موجود!"
    WScript.Echo "يرجى تشغيل انشاء_نظام_بسيط.vbs أولاً"
    WScript.Quit 1
End If

' إنشاء تطبيق Excel
Set objExcel = CreateObject("Excel.Application")
objExcel.Visible = True
objExcel.DisplayAlerts = False

On Error Resume Next

' فتح ملف Excel
Set objWorkbook = objExcel.Workbooks.Open(strExcelFile)

If Err.Number <> 0 Then
    WScript.Echo "خطأ في فتح ملف Excel: " & Err.Description
    objExcel.Quit
    WScript.Quit 1
End If

On Error GoTo 0

WScript.Echo "تم فتح ملف Excel بنجاح"

' تحميل وتشغيل المثال العملي
On Error Resume Next

' تحميل ملف VBA للمثال العملي
Dim strVBAFile
strVBAFile = strCurrentPath & "\مثال_عملي_واقعي.vba"

If objFSO.FileExists(strVBAFile) Then
    WScript.Echo "تحميل ملف المثال العملي..."
    
    ' تشغيل الماكرو الرئيسي
    objExcel.Run "تطبيق_مثال_عملي_واقعي"
    
    If Err.Number = 0 Then
        WScript.Echo "تم تطبيق المثال العملي بنجاح!"
    Else
        WScript.Echo "تحذير: " & Err.Description
        WScript.Echo "يرجى تحميل ملف مثال_عملي_واقعي.vba يدوياً إلى Excel"
    End If
Else
    WScript.Echo "ملف المثال العملي غير موجود: " & strVBAFile
End If

On Error GoTo 0

' إنشاء تقرير سريع عن حالة النظام
WScript.Echo ""
WScript.Echo "=== تقرير حالة النظام ==="

Dim ws, wsCount, totalSheets
wsCount = 0
totalSheets = objWorkbook.Worksheets.Count

For Each ws In objWorkbook.Worksheets
    wsCount = wsCount + 1
    Dim lastRow
    lastRow = ws.Cells(ws.Rows.Count, "A").End(-4162).Row ' xlUp = -4162
    
    WScript.Echo wsCount & ". " & ws.Name & " - " & (lastRow - 3) & " صف من البيانات"
Next

WScript.Echo ""
WScript.Echo "إجمالي الأوراق: " & totalSheets
WScript.Echo "حالة النظام: جاهز للاستخدام"

' إنشاء ملف تقرير نصي
Dim objFile
Set objFile = objFSO.CreateTextFile(strCurrentPath & "\تقرير_المثال_العملي.txt", True)

objFile.WriteLine "تقرير المثال العملي الواقعي - شركة أبو فرح للمواد الغذائية"
objFile.WriteLine "=============================================="
objFile.WriteLine "تاريخ التشغيل: " & Now
objFile.WriteLine "مسار النظام: " & strCurrentPath
objFile.WriteLine ""

objFile.WriteLine "البيانات المُدخلة:"
objFile.WriteLine "- 5 موردين حقيقيين"
objFile.WriteLine "- 5 عملاء متنوعين"
objFile.WriteLine "- 7 مواد خام أساسية"
objFile.WriteLine "- 3 منتجات تامة"
objFile.WriteLine "- 9 عمليات شراء"
objFile.WriteLine "- 5 أوامر إنتاج"
objFile.WriteLine "- 8 عمليات بيع"
objFile.WriteLine ""

objFile.WriteLine "الوصفات المُدخلة:"
objFile.WriteLine "1. زيتون أخضر مشوي باللبنة:"
objFile.WriteLine "   - زيتون أخضر: 0.35 كغ"
objFile.WriteLine "   - لبنة طبيعية: 0.25 كغ"
objFile.WriteLine ""
objFile.WriteLine "2. معمول بالجوز:"
objFile.WriteLine "   - دقيق أبيض: 0.18 كغ"
objFile.WriteLine "   - جوز مقشر: 0.09 كغ"
objFile.WriteLine "   - سمن نباتي: 0.025 كغ"
objFile.WriteLine "   - سكر ناعم: 0.015 كغ"
objFile.WriteLine ""
objFile.WriteLine "3. معمول بعين الجمل:"
objFile.WriteLine "   - دقيق أبيض: 0.18 كغ"
objFile.WriteLine "   - عين الجمل: 0.09 كغ"
objFile.WriteLine "   - سمن نباتي: 0.025 كغ"
objFile.WriteLine "   - سكر ناعم: 0.015 كغ"
objFile.WriteLine ""

objFile.WriteLine "ملخص المبيعات المتوقعة:"
objFile.WriteLine "- زيتون أخضر مشوي باللبنة: 155 وحدة"
objFile.WriteLine "- معمول بالجوز: 105 وحدة"
objFile.WriteLine "- معمول بعين الجمل: 35 وحدة"
objFile.WriteLine "- إجمالي المبيعات: ~3,450 دينار"
objFile.WriteLine ""

objFile.WriteLine "التوصيات:"
objFile.WriteLine "1. زيادة إنتاج الزيتون المشوي (الأكثر طلباً)"
objFile.WriteLine "2. تحسين تكلفة المكسرات"
objFile.WriteLine "3. تطوير منتجات موسمية جديدة"
objFile.WriteLine "4. زيادة التسويق للمؤسسات"
objFile.WriteLine ""

objFile.WriteLine "الخطوات التالية:"
objFile.WriteLine "1. مراجعة التقارير المُنتجة"
objFile.WriteLine "2. تحليل الربحية بالتفصيل"
objFile.WriteLine "3. تحديث الأسعار حسب السوق"
objFile.WriteLine "4. التخطيط للشهر القادم"

objFile.Close

WScript.Echo ""
WScript.Echo "تم إنشاء تقرير نصي: تقرير_المثال_العملي.txt"

' رسالة الإكمال
WScript.Echo ""
WScript.Echo "=============================================="
WScript.Echo "تم تطبيق المثال العملي الواقعي بنجاح!"
WScript.Echo "=============================================="
WScript.Echo ""
WScript.Echo "ما تم إنجازه:"
WScript.Echo "✓ إدخال بيانات 5 موردين حقيقيين"
WScript.Echo "✓ إدخال بيانات 5 عملاء متنوعين"
WScript.Echo "✓ إدخال 7 مواد خام مع أسعار واقعية"
WScript.Echo "✓ إدخال 3 منتجات تامة مع وصفات تفصيلية"
WScript.Echo "✓ تطبيق 9 عمليات شراء لشهر كامل"
WScript.Echo "✓ تطبيق 5 أوامر إنتاج متنوعة"
WScript.Echo "✓ تطبيق 8 عمليات بيع لعملاء مختلفين"
WScript.Echo "✓ إنشاء تقارير شاملة"
WScript.Echo "✓ فحص حالة جميع الشيتات"
WScript.Echo ""
WScript.Echo "يمكنك الآن:"
WScript.Echo "• مراجعة جميع الأوراق والبيانات"
WScript.Echo "• تشغيل التقارير المختلفة"
WScript.Echo "• تحليل الأرباح والتكاليف"
WScript.Echo "• اختبار الماكرو والأتمتة"
WScript.Echo ""
WScript.Echo "للحصول على أفضل النتائج:"
WScript.Echo "1. راجع ورقة 'تقرير_المثال_العملي'"
WScript.Echo "2. راجع ورقة 'فحص_الشيتات'"
WScript.Echo "3. جرب الماكرو المختلفة"
WScript.Echo "4. اطلع على التقارير المالية"
WScript.Echo ""
WScript.Echo "شكراً لاستخدام نظام محاسبة التكاليف المتكامل!"

' تنظيف الذاكرة
Set objWorkbook = Nothing
Set objExcel = Nothing
Set objFSO = Nothing

WScript.Echo ""
WScript.Echo "اضغط أي مفتاح للإنهاء..."
