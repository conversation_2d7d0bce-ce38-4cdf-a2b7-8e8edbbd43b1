# نظام محاسبة التكاليف المتكامل لمصانع المواد الغذائية

## نظرة عامة

نظام محاسبة تكاليف متكامل مصمم خصيصاً لمصانع المواد الغذائية باستخدام Microsoft Excel. يدعم النظام إنتاج منتجات مثل الزيتون الأخضر المشوي باللبنة والمعمول بالجوز وعين الجمل.

## المميزات الرئيسية

### 🏭 إدارة الإنتاج
- إنشاء وصفات تفصيلية للمنتجات
- إدارة أوامر الإنتاج مع حساب التكاليف
- تتبع تكاليف المواد الخام والعمالة والتكاليف الإضافية
- ترحيل المنتجات التامة إلى المخزون

### 📦 إدارة المخزون
- تتبع مخزون المواد الخام والمنتجات التامة
- تحديث الأرصدة تلقائياً بعد كل عملية
- تنبيهات للمواد منخفضة المخزون
- حساب متوسط التكلفة وقيمة المخزون

### 🛒 إدارة المشتريات
- تسجيل مشتريات المواد الخام
- ربط المشتريات بالموردين
- تحديث أسعار المواد تلقائياً
- تتبع فواتير الشراء

### 💰 إدارة المبيعات
- تسجيل مبيعات المنتجات التامة
- ربط المبيعات بالعملاء
- حساب الأرباح والخسائر
- تحديث مخزون المنتجات بعد البيع

### 📊 التقارير والتحليل
- تقارير تكلفة الإنتاج
- تقارير أرصدة المخزون
- قوائم الأرباح والخسائر
- تحليل الربحية بالمنتج

## متطلبات النظام

- Microsoft Excel 2016 أو أحدث
- نظام التشغيل Windows 10 أو أحدث
- دعم اللغة العربية
- تفعيل الماكرو في Excel

## التثبيت والتشغيل

### 1. التثبيت السريع
```bash
# تشغيل سكريپت الإنشاء
cscript انشاء_نظام_بسيط.vbs
```

### 2. التثبيت اليدوي
1. نسخ جميع ملفات VBA إلى مجلد المشروع
2. فتح Excel وتفعيل الماكرو
3. استيراد ملفات VBA إلى محرر Visual Basic
4. تشغيل الإجراء `انشاء_نظام_محاسبة_التكاليف()`

## هيكل المشروع

```
نظام_محاسبة_التكاليف/
├── نظام_محاسبة_التكاليف_متكامل.xlsx    # الملف الرئيسي
├── اعداد_النظام_الكامل.vba                # إعداد النظام الأساسي
├── اعداد_الجداول_المتقدمة.vba            # إعداد الجداول المتقدمة
├── اعداد_الانتاج_والمبيعات.vba           # إعداد الإنتاج والمبيعات
├── اعداد_التقارير.vba                    # إعداد التقارير
├── ماكرو_الاتمتة.vba                     # ماكرو الأتمتة
├── تحسين_التنسيق.vba                     # تحسين التنسيق
├── دليل_المستخدم_نظام_محاسبة_التكاليف.md  # دليل المستخدم الكامل
├── تعليمات_التشغيل_السريع.md             # تعليمات سريعة
└── README.md                             # هذا الملف
```

## الأوراق المتضمنة

| الورقة | الوصف |
|--------|--------|
| لوحة_التحكم | الواجهة الرئيسية للنظام |
| الموردين | بيانات الموردين |
| العملاء | بيانات العملاء |
| المواد_الخام | كتالوج المواد الخام |
| المنتجات_التامة | كتالوج المنتجات النهائية |
| وحدات_القياس | وحدات القياس المستخدمة |
| مراكز_التكلفة | مراكز التكلفة في المصنع |
| المشتريات | سجل المشتريات |
| مخزون_المواد_الخام | أرصدة المواد الخام |
| الوصفات | وصفات المنتجات |
| اوامر_الانتاج | أوامر الإنتاج |
| مخزون_المنتجات_التامة | أرصدة المنتجات التامة |
| المبيعات | سجل المبيعات |
| تقرير_التكاليف | تقرير تكاليف الإنتاج |
| تقرير_المخزون | تقرير أرصدة المخزون |
| التقارير_المالية | التقارير المالية والمحاسبية |

## الماكرو المتاح

### ماكرو الأتمتة
- `تحديث_مخزون_المواد_الخام()` - تحديث مخزون المواد الخام
- `حساب_تكلفة_الانتاج()` - حساب تكلفة الإنتاج
- `تحديث_مخزون_المنتجات_التامة()` - تحديث مخزون المنتجات
- `تحديث_مخزون_بعد_المبيعات()` - تحديث المخزون بعد البيع
- `تحديث_شامل_للنظام()` - تحديث شامل لجميع البيانات
- `انشاء_تقرير_سريع()` - إنشاء تقرير سريع

### ماكرو التنسيق
- `تطبيق_التنسيق_الاحترافي()` - تطبيق تنسيق احترافي
- `اضافة_شريط_ادوات_مخصص()` - إضافة شريط أدوات مخصص

## المنتجات المدعومة

### 1. الزيتون الأخضر المشوي باللبنة
**المكونات:**
- زيتون أخضر: 0.3 كيلوغرام
- لبنة طبيعية: 0.2 كيلوغرام

### 2. المعمول بالجوز
**المكونات:**
- دقيق أبيض: 0.15 كيلوغرام
- جوز مقشر: 0.08 كيلوغرام
- سمن نباتي: 0.02 كيلوغرام
- سكر ناعم: 0.01 كيلوغرام

### 3. المعمول بعين الجمل
**المكونات:**
- دقيق أبيض: 0.15 كيلوغرام
- عين الجمل: 0.08 كيلوغرام
- سمن نباتي: 0.02 كيلوغرام
- سكر ناعم: 0.01 كيلوغرام

## الاستخدام السريع

### 1. البدء
1. افتح ملف `نظام_محاسبة_التكاليف_متكامل.xlsx`
2. تأكد من تفعيل الماكرو
3. ابدأ من ورقة "لوحة_التحكم"

### 2. إدخال البيانات الأساسية
1. أدخل بيانات الموردين والعملاء
2. أدخل بيانات المواد الخام والمنتجات
3. تأكد من صحة الوصفات

### 3. العمليات اليومية
1. سجل المشتريات وشغل ماكرو تحديث المخزون
2. أنشئ أوامر الإنتاج واحسب التكاليف
3. سجل المبيعات وحدث المخزون

## الدعم والصيانة

### النسخ الاحتياطي
- احفظ نسخة احتياطية يومياً
- استخدم أسماء ملفات تحتوي على التاريخ
- احتفظ بنسخ في أماكن متعددة

### الأمان
- احم الملف بكلمة مرور
- قيد الوصول للمستخدمين المخولين
- راجع الصلاحيات بانتظام

### التحديثات
- راجع الصيغ والماكرو دورياً
- حدث البيانات الأساسية عند الحاجة
- تابع التحديثات الجديدة

## المساهمة

نرحب بالمساهمات لتحسين النظام:

1. Fork المشروع
2. أنشئ فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- **المطور**: نظام محاسبة التكاليف
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: www.costaccounting.com

## الشكر والتقدير

- شكر خاص لجميع المساهمين في تطوير النظام
- شكر لمجتمع Excel العربي على الدعم والمساعدة
- شكر للمصانع التي ساعدت في اختبار النظام

---

**ملاحظة**: هذا النظام مصمم لتلبية احتياجات مصانع المواد الغذائية الصغيرة والمتوسطة. يمكن تخصيصه وتطويره حسب الاحتياجات الخاصة.
