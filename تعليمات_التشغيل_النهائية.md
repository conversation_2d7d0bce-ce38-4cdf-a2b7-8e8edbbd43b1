# تعليمات التشغيل النهائية
## نظام محاسبة التكاليف لمصنع المواد الغذائية

---

## 🎯 نظرة عامة على النظام

تم إنشاء نظام محاسبة التكاليف المتكامل لمصنع المواد الغذائية بنجاح! النظام يدعم إنتاج منتجات مثل:
- **زيتون أخضر مشوي باللبنة**
- **معمول بالجوز وعين الجمل**
- وأي منتجات غذائية أخرى

---

## 🚀 خطوات التشغيل

### الخطوة 1: إنشاء قاعدة البيانات
```vba
' في Microsoft Access، اضغط Alt+F11 لفتح محرر VBA
' انسخ والصق الكود التالي واضغط F5 لتشغيله:

Sub تشغيل_النظام_كاملا()
    Call انشاء_النظام_كاملا
End Sub
```

### الخطوة 2: تشغيل الملفات بالترتيب
1. **انشاء_قاعدة_البيانات.vba** - إنشاء الجداول والعلاقات
2. **الفهارس_والبيانات_الاساسية.sql** - إنشاء الفهارس والبيانات الأولية
3. **انشاء_النماذج_الاساسية.vba** - إنشاء نماذج البيانات الأساسية
4. **نماذج_المشتريات_والمخزون.vba** - إنشاء نماذج المشتريات
5. **نماذج_الانتاج_والوصفات.vba** - إنشاء نماذج الإنتاج
6. **نماذج_المبيعات.vba** - إنشاء نماذج المبيعات
7. **النموذج_الرئيسي.vba** - إنشاء الواجهة الرئيسية
8. **الاستعلامات_والتقارير.sql** - إنشاء الاستعلامات والتقارير
9. **اجراءات_النظام_والاختبار.vba** - إجراءات الاختبار والصيانة

### الخطوة 3: تشغيل البيانات التجريبية
```vba
Call ادخال_بيانات_تجريبية()
Call اختبار_النظام()
```

---

## 📋 مكونات النظام

### 🏗️ الجداول الأساسية
- **الوحدات** - وحدات القياس (كيلو، جرام، لتر، قطعة)
- **الموردين** - بيانات الموردين
- **العملاء** - بيانات العملاء
- **المواد_الخام** - المواد الخام المستخدمة
- **المنتجات_التامة** - المنتجات النهائية
- **المخازن** - مخازن المواد والمنتجات
- **انواع_التكاليف** - أنواع التكاليف المختلفة

### 📦 جداول المخزون
- **ارصدة_المواد_الخام** - أرصدة المواد الخام
- **ارصدة_المنتجات_التامة** - أرصدة المنتجات التامة
- **حركات_المخزون** - تسجيل جميع حركات المخزون

### 🏭 جداول الإنتاج
- **وصفات_المنتجات** - وصفات تصنيع المنتجات
- **تفاصيل_الوصفات** - مكونات كل وصفة
- **اوامر_الانتاج** - أوامر الإنتاج
- **تكاليف_اوامر_الانتاج** - تكاليف إضافية للإنتاج

### 💰 جداول المالية
- **فواتير_المشتريات** - فواتير شراء المواد الخام
- **تفاصيل_فواتير_المشتريات** - تفاصيل المشتريات
- **فواتير_المبيعات** - فواتير بيع المنتجات
- **تفاصيل_فواتير_المبيعات** - تفاصيل المبيعات

---

## 🖥️ النماذج والواجهات

### النموذج الرئيسي
- **النموذج_الرئيسي** - الواجهة الرئيسية للنظام
- **لوحة_المعلومات** - ملخص سريع للنشاط

### نماذج البيانات الأساسية
- نموذج_الوحدات
- نموذج_الموردين
- نموذج_العملاء
- نموذج_المواد_الخام
- نموذج_المنتجات_التامة
- نموذج_المخازن
- نموذج_انواع_التكاليف

### نماذج العمليات
- **نموذج_فواتير_المشتريات** - إدخال فواتير المشتريات
- **نموذج_وصفات_المنتجات** - إدارة وصفات الإنتاج
- **نموذج_اوامر_الانتاج** - إدارة أوامر الإنتاج
- **نموذج_فواتير_المبيعات** - إدخال فواتير المبيعات

---

## 📊 التقارير والاستعلامات

### استعلامات المخزون
- **عرض_ارصدة_المواد_الخام_تفصيلي**
- **عرض_ارصدة_المنتجات_التامة_تفصيلي**
- **تنبيه_المواد_تحت_الحد_الادنى**

### استعلامات التكاليف
- **عرض_تكاليف_اوامر_الانتاج_تفصيلي**
- **تحليل_التكاليف_حسب_النوع**
- **تكلفة_المواد_المستهلكة_في_الانتاج**

### استعلامات المبيعات والمشتريات
- **عرض_فواتير_المشتريات_تفصيلي**
- **عرض_فواتير_المبيعات_تفصيلي**
- **تحليل_المشتريات_حسب_المورد**
- **تحليل_المبيعات_حسب_العميل**

### تقارير الربحية
- **تقرير_الربحية_الشامل**

---

## ⚙️ العمليات الأساسية

### 1. إدخال المواد الخام (المشتريات)
1. افتح **نموذج_فواتير_المشتريات**
2. اختر المورد والمخزن
3. أدخل تفاصيل المواد المشتراة
4. احفظ وأكد الفاتورة
5. سيتم تحديث أرصدة المخزون تلقائياً

### 2. إنشاء وصفة منتج جديد
1. افتح **نموذج_وصفات_المنتجات**
2. اختر المنتج وأدخل اسم الوصفة
3. حدد كمية الإنتاج
4. أدخل المواد الخام المطلوبة في النموذج الفرعي
5. احفظ الوصفة

### 3. إنشاء أمر إنتاج
1. افتح **نموذج_اوامر_الانتاج**
2. اختر المنتج والوصفة
3. حدد كمية الإنتاج والمخزن
4. أدخل التكاليف الإضافية (عمالة، كهرباء، غاز)
5. احفظ الأمر وابدأ الإنتاج
6. عند الانتهاء، أكد الأمر لتحديث المخزون

### 4. بيع المنتجات
1. افتح **نموذج_فواتير_المبيعات**
2. اختر العميل والمخزن
3. أدخل المنتجات المباعة وكمياتها
4. احسب المبالغ وأكد الفاتورة
5. سيتم صرف المنتجات من المخزون تلقائياً

---

## 🔧 أدوات الصيانة

### النسخ الاحتياطي
```vba
Call انشاء_نسخة_احتياطية()
```

### تحسين الأداء
```vba
Call تحسين_اداء_النظام()
```

### اختبار النظام
```vba
Call اختبار_النظام()
```

---

## 📈 مؤشرات الأداء

النظام يوفر مؤشرات أداء مهمة:
- **أرصدة المخزون الحالية**
- **المواد تحت الحد الأدنى**
- **تكاليف الإنتاج**
- **هوامش الربح**
- **أداء المبيعات**

---

## 🎯 المميزات الرئيسية

✅ **إدارة شاملة للمخزون** - تتبع دقيق لجميع المواد والمنتجات  
✅ **حساب التكاليف التلقائي** - حساب تكاليف الإنتاج بدقة  
✅ **إدارة الوصفات** - وصفات مفصلة لكل منتج  
✅ **تتبع الربحية** - حساب هوامش الربح لكل منتج  
✅ **تقارير شاملة** - تقارير مفصلة لجميع العمليات  
✅ **واجهة عربية** - واجهة مستخدم باللغة العربية بالكامل  
✅ **نظام تنبيهات** - تنبيهات للمواد تحت الحد الأدنى  
✅ **أمان البيانات** - نظام نسخ احتياطي متقدم  

---

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع **دليل_المستخدم_نظام_محاسبة_التكاليف.md**
2. استخدم النماذج التجريبية للتعلم
3. اتبع الأمثلة المرفقة في البيانات التجريبية

---

## 🏁 ملاحظات نهائية

- تأكد من إنشاء نسخ احتياطية دورية
- راجع التقارير بانتظام لمتابعة الأداء
- حدث أسعار المواد الخام حسب السوق
- راقب مستويات المخزون باستمرار
- استخدم التقارير لاتخاذ قرارات مدروسة

**🎉 مبروك! نظام محاسبة التكاليف جاهز للاستخدام!**
