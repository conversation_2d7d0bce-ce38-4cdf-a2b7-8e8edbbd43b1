# تعليمات التشغيل النهائية - نظام محاسبة التكاليف المتكامل

## 🚀 البدء السريع

### الخطوة 1: إنشاء النظام
```bash
# تشغيل سكريپت الإنشاء
cscript انشاء_نظام_بسيط.vbs
```

### الخطوة 2: فتح النظام
1. افتح ملف `نظام_محاسبة_التكاليف_متكامل.xlsx`
2. فعّل الماكرو عند السؤال
3. ابدأ من ورقة "لوحة_التحكم"

### الخطوة 3: اختبار النظام
1. اضغط `Alt + F11` لفتح محرر VBA
2. استورد ملف `اختبار_النظام.vba`
3. شغل الإجراء `اختبار_النظام_الشامل()`

## 📋 قائمة التحقق الأولية

### ✅ قبل البدء
- [ ] تأكد من تثبيت Excel 2016 أو أحدث
- [ ] فعّل دعم اللغة العربية في Windows
- [ ] تأكد من تفعيل الماكرو في Excel
- [ ] أنشئ مجلد للنسخ الاحتياطية

### ✅ إعداد البيانات الأساسية
- [ ] أدخل بيانات الموردين في ورقة "الموردين"
- [ ] أدخل بيانات العملاء في ورقة "العملاء"
- [ ] أدخل بيانات المواد الخام في ورقة "المواد_الخام"
- [ ] أدخل بيانات المنتجات في ورقة "المنتجات_التامة"
- [ ] تحقق من صحة الوصفات في ورقة "الوصفات"

### ✅ اختبار الوظائف
- [ ] اختبر تسجيل مشترى واحد
- [ ] اختبر إنشاء أمر إنتاج واحد
- [ ] اختبر تسجيل مبيعة واحدة
- [ ] تحقق من تحديث المخزون تلقائياً
- [ ] راجع التقارير المُنتجة

## 🔧 إعدادات Excel المطلوبة

### تفعيل الماكرو
1. File → Options → Trust Center → Trust Center Settings
2. Macro Settings → Enable all macros
3. أعد تشغيل Excel

### إعدادات اللغة
1. File → Options → Language
2. أضف العربية كلغة تحرير
3. اجعل العربية اللغة الافتراضية

### إعدادات العرض
1. View → Gridlines (إلغاء التحديد)
2. View → Formula Bar (تفعيل)
3. View → Freeze Panes (سيتم تطبيقه تلقائياً)

## 📊 دليل الاستخدام اليومي

### صباح كل يوم
1. **افتح النظام**
   - افتح ملف Excel
   - تحقق من ورقة "لوحة_التحكم"
   - راجع التنبيهات إن وجدت

2. **راجع المخزون**
   - انتقل إلى "تقرير_المخزون"
   - تحقق من المواد منخفضة المخزون
   - خطط للمشتريات المطلوبة

### تسجيل المشتريات
1. انتقل إلى ورقة "المشتريات"
2. أدخل البيانات في السطر التالي:
   ```
   رقم الفاتورة | التاريخ | كود المورد | كود المادة | الكمية | السعر
   ```
3. شغل ماكرو `تحديث_مخزون_المواد_الخام()`

### إنشاء أمر إنتاج
1. انتقل إلى ورقة "اوامر_الانتاج"
2. أدخل تفاصيل الأمر
3. شغل ماكرو `حساب_تكلفة_الانتاج()`
4. عند اكتمال الإنتاج:
   - غيّر الحالة إلى "مكتمل"
   - شغل ماكرو `تحديث_مخزون_المنتجات_التامة()`

### تسجيل المبيعات
1. انتقل إلى ورقة "المبيعات"
2. أدخل تفاصيل البيع
3. شغل ماكرو `تحديث_مخزون_بعد_المبيعات()`

### نهاية اليوم
1. شغل `تحديث_شامل_للنظام()`
2. راجع "التقارير_المالية"
3. احفظ نسخة احتياطية

## 🛠️ الماكرو والأتمتة

### الماكرو الأساسي
| الماكرو | الوظيفة | متى يُستخدم |
|---------|---------|-------------|
| `تحديث_مخزون_المواد_الخام()` | تحديث مخزون المواد | بعد كل مشترى |
| `حساب_تكلفة_الانتاج()` | حساب تكلفة الإنتاج | عند إنشاء أمر إنتاج |
| `تحديث_مخزون_المنتجات_التامة()` | تحديث مخزون المنتجات | عند اكتمال الإنتاج |
| `تحديث_مخزون_بعد_المبيعات()` | تحديث المخزون | بعد كل مبيعة |
| `تحديث_شامل_للنظام()` | تحديث شامل | نهاية اليوم |

### تشغيل الماكرو
**الطريقة 1: من محرر VBA**
1. اضغط `Alt + F11`
2. اختر الماكرو المطلوب
3. اضغط `F5`

**الطريقة 2: من شريط الأدوات**
1. Developer Tab → Macros
2. اختر الماكرو
3. اضغط Run

**الطريقة 3: اختصارات لوحة المفاتيح**
- `Ctrl + Shift + U` للتحديث الشامل
- `Ctrl + Shift + R` للتقرير السريع

## 📈 التقارير والتحليل

### التقارير اليومية
1. **تقرير المخزون**
   - أرصدة المواد الخام
   - أرصدة المنتجات التامة
   - المواد منخفضة المخزون

2. **تقرير التكاليف**
   - تكلفة إنتاج كل منتج
   - توزيع التكاليف
   - مقارنة التكاليف

### التقارير الأسبوعية
1. **تقرير المبيعات**
   - إجمالي المبيعات
   - المبيعات بالمنتج
   - المبيعات بالعميل

2. **تقرير الربحية**
   - الربح الإجمالي
   - الربح بالمنتج
   - هامش الربح

### التقارير الشهرية
1. **قائمة الأرباح والخسائر**
2. **تحليل التكاليف**
3. **تقرير الأداء**

## 🔍 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### المشكلة: الماكرو لا يعمل
**الأسباب المحتملة:**
- الماكرو غير مفعل
- خطأ في البيانات
- ملف تالف

**الحلول:**
1. تحقق من إعدادات الماكرو
2. راجع البيانات المدخلة
3. أعد تشغيل Excel
4. استخدم نسخة احتياطية

#### المشكلة: خطأ في الصيغ
**الأسباب المحتملة:**
- مراجع خلايا خاطئة
- بيانات مفقودة
- تنسيق خاطئ

**الحلول:**
1. تحقق من مراجع الخلايا
2. تأكد من وجود البيانات المطلوبة
3. راجع تنسيق الخلايا
4. استخدم F9 لإعادة الحساب

#### المشكلة: بطء في الأداء
**الأسباب المحتملة:**
- كمية كبيرة من البيانات
- صيغ معقدة
- ذاكرة غير كافية

**الحلول:**
1. احذف البيانات القديمة
2. أغلق البرامج الأخرى
3. استخدم `Application.ScreenUpdating = False`
4. قسم البيانات إلى ملفات منفصلة

## 💾 النسخ الاحتياطي والأمان

### استراتيجية النسخ الاحتياطي
1. **يومياً**: نسخة في نفس الجهاز
2. **أسبوعياً**: نسخة في جهاز خارجي
3. **شهرياً**: نسخة في السحابة

### تسمية الملفات
```
نظام_التكاليف_YYYY_MM_DD.xlsx
مثال: نظام_التكاليف_2024_01_15.xlsx
```

### الأمان
1. **حماية الملف**
   - File → Info → Protect Workbook → Encrypt with Password

2. **حماية الأوراق**
   - Review → Protect Sheet

3. **صلاحيات المستخدمين**
   - حدد من يمكنه تعديل البيانات
   - حدد من يمكنه عرض التقارير فقط

## 📞 الدعم والمساعدة

### الدعم الذاتي
1. راجع دليل المستخدم الكامل
2. استخدم ميزة البحث في Excel
3. راجع تعليقات الخلايا

### الدعم التقني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +962-6-1234567
- **ساعات العمل**: 8:00 ص - 5:00 م (الأحد - الخميس)

### الموارد الإضافية
- **الموقع الإلكتروني**: www.costaccounting.com
- **قناة YouTube**: نظام محاسبة التكاليف
- **مجموعة Facebook**: مستخدمو نظام التكاليف

## 🎯 نصائح للنجاح

### أفضل الممارسات
1. **الانتظام**: أدخل البيانات يومياً
2. **الدقة**: راجع البيانات قبل الحفظ
3. **التنظيم**: استخدم أكواد موحدة
4. **المتابعة**: راجع التقارير بانتظام

### تحسين الأداء
1. احذف البيانات القديمة غير المطلوبة
2. استخدم الاختصارات
3. تعلم الماكرو الأساسي
4. نظم البيانات بشكل منطقي

### التطوير المستقبلي
1. تعلم VBA لتخصيص النظام
2. اربط النظام بقواعد بيانات خارجية
3. أضف تقارير جديدة حسب الحاجة
4. طور واجهة مستخدم متقدمة

---

**تهانينا! أصبح لديك الآن نظام محاسبة تكاليف متكامل وجاهز للاستخدام.**

**للحصول على أفضل النتائج، ابدأ بالبيانات التجريبية واختبر جميع الوظائف قبل إدخال البيانات الفعلية.**
