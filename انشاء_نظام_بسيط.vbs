On Error Resume Next

' إنشاء كائن Excel
Dim xlApp
Set xlApp = CreateObject("Excel.Application")

If Err.Number <> 0 Then
    WScript.Echo "خطأ في تشغيل Excel: " & Err.Description
    WScript.Quit
End If

xlApp.Visible = True
xlApp.DisplayAlerts = False

' إنشاء مصنف جديد
Dim xlWorkbook
Set xlWorkbook = xlApp.Workbooks.Add

' حذف الأوراق الافتراضية
Do While xlWorkbook.Worksheets.Count > 1
    xlWorkbook.Worksheets(xlWorkbook.Worksheets.Count).Delete
Loop

' إعادة تسمية الورقة الأولى
xlWorkbook.Worksheets(1).Name = "لوحة_التحكم"

' إضافة الأوراق المطلوبة
Dim sheetNames
sheetNames = Array("الموردين", "العملاء", "المواد_الخام", "المنتجات_التامة", "وحدات_القياس", "مراكز_التكلفة", "المشتريات", "مخزون_المواد_الخام", "الوصفات", "اوامر_الانتاج", "مخزون_المنتجات_التامة", "المبيعات", "تقرير_التكاليف", "تقرير_المخزون", "التقارير_المالية")

Dim i
For i = 0 To UBound(sheetNames)
    xlWorkbook.Worksheets.Add.Name = sheetNames(i)
Next

' ترتيب الأوراق
xlWorkbook.Worksheets("لوحة_التحكم").Move xlWorkbook.Worksheets(1)

' إعداد لوحة التحكم
Dim ws
Set ws = xlWorkbook.Worksheets("لوحة_التحكم")

ws.Range("A1").Value = "نظام محاسبة التكاليف المتكامل"
ws.Range("A1").Font.Size = 18
ws.Range("A1").Font.Bold = True
ws.Range("A1:F1").Merge
ws.Range("A1").HorizontalAlignment = -4108

ws.Range("A3").Value = "مصنع المواد الغذائية"
ws.Range("A3").Font.Size = 14
ws.Range("A3").Font.Bold = True
ws.Range("A3:F3").Merge
ws.Range("A3").HorizontalAlignment = -4108

ws.Range("B6").Value = "البيانات الأساسية"
ws.Range("B7").Value = "المشتريات والمخزون"
ws.Range("B8").Value = "الإنتاج والوصفات"
ws.Range("B9").Value = "المبيعات"
ws.Range("B10").Value = "التقارير المالية"

ws.Range("B6:B10").Font.Bold = True

ws.Range("A13").Value = "المنتجات المدعومة:"
ws.Range("A14").Value = "• الزيتون الأخضر المشوي باللبنة"
ws.Range("A15").Value = "• المعمول بالجوز"
ws.Range("A16").Value = "• المعمول بعين الجمل"

ws.Range("A13:A16").Font.Bold = True

' إعداد جدول الموردين
Set ws = xlWorkbook.Worksheets("الموردين")
ws.Range("A1").Value = "جدول الموردين"
ws.Range("A1").Font.Size = 16
ws.Range("A1").Font.Bold = True

ws.Range("A3").Value = "كود المورد"
ws.Range("B3").Value = "اسم المورد"
ws.Range("C3").Value = "العنوان"
ws.Range("D3").Value = "الهاتف"
ws.Range("E3").Value = "البريد الإلكتروني"
ws.Range("F3").Value = "ملاحظات"

ws.Range("A3:F3").Font.Bold = True

ws.Range("A4").Value = "SUP001"
ws.Range("B4").Value = "شركة الزيتون الذهبي"
ws.Range("C4").Value = "عمان - الأردن"
ws.Range("D4").Value = "06-1234567"
ws.Range("E4").Value = "<EMAIL>"

' إعداد جدول المواد الخام
Set ws = xlWorkbook.Worksheets("المواد_الخام")
ws.Range("A1").Value = "جدول المواد الخام"
ws.Range("A1").Font.Size = 16
ws.Range("A1").Font.Bold = True

ws.Range("A3").Value = "كود المادة"
ws.Range("B3").Value = "اسم المادة"
ws.Range("C3").Value = "وحدة القياس"
ws.Range("D3").Value = "السعر الوحدة"
ws.Range("E3").Value = "الحد الأدنى"
ws.Range("F3").Value = "المورد الرئيسي"

ws.Range("A3:F3").Font.Bold = True

ws.Range("A4").Value = "RM001"
ws.Range("B4").Value = "زيتون أخضر"
ws.Range("C4").Value = "كيلوغرام"
ws.Range("D4").Value = 5.5
ws.Range("E4").Value = 100
ws.Range("F4").Value = "SUP001"

ws.Range("A5").Value = "RM002"
ws.Range("B5").Value = "لبنة طبيعية"
ws.Range("C5").Value = "كيلوغرام"
ws.Range("D5").Value = 8.0
ws.Range("E5").Value = 50
ws.Range("F5").Value = "SUP001"

' حفظ الملف
xlWorkbook.SaveAs "نظام_محاسبة_التكاليف_متكامل.xlsx"

WScript.Echo "تم إنشاء نظام محاسبة التكاليف بنجاح!"
WScript.Echo "اسم الملف: نظام_محاسبة_التكاليف_متكامل.xlsx"
WScript.Echo "عدد الأوراق: " & xlWorkbook.Worksheets.Count

Set ws = Nothing
Set xlWorkbook = Nothing
Set xlApp = Nothing
