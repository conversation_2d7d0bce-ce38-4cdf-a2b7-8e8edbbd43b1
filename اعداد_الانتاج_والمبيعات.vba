Sub اعداد_جدول_اوامر_الانتاج(ws As Worksheet)
    ' إعداد جدول أوامر الإنتاج
    With ws
        .Range("A1").Value = "أوامر الإنتاج"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:J1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "رقم الأمر"
        .Range("B3").Value = "التاريخ"
        .Range("C3").Value = "كود المنتج"
        .Range("D3").Value = "اسم المنتج"
        .Range("E3").Value = "الكمية المطلوبة"
        .Range("F3").Value = "تكلفة المواد الخام"
        .Range("G3").Value = "تكلفة العمالة"
        .Range("H3").Value = "التكاليف الإضافية"
        .Range("I3").Value = "إجمالي التكلفة"
        .Range("J3").Value = "الحالة"
        
        ' تنسيق العناوين
        .Range("A3:J3").Font.Bold = True
        .Range("A3:J3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:J3").Borders.LineStyle = xlContinuous
        
        ' إضافة صيغة حساب إجمالي التكلفة
        .Range("I4").Formula = "=F4+G4+H4"
        
        ' إضافة بيانات تجريبية
        .Range("A4").Value = "PRO001"
        .Range("B4").Value = Date
        .Range("C4").Value = "FG001"
        .Range("D4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("E4").Value = 100
        .Range("F4").Value = 275  ' (0.3*5.5 + 0.2*8) * 100
        .Range("G4").Value = 50   ' تكلفة العمالة
        .Range("H4").Value = 25   ' تكاليف إضافية
        .Range("J4").Value = "قيد التنفيذ"
        
        .Range("A5").Value = "PRO002"
        .Range("B5").Value = Date
        .Range("C5").Value = "FG002"
        .Range("D5").Value = "معمول بالجوز"
        .Range("E5").Value = 50
        .Range("F5").Value = 162.5  ' (0.15*2.5 + 0.08*15 + 0.02*4) * 50
        .Range("G5").Value = 75     ' تكلفة العمالة
        .Range("H5").Value = 30     ' تكاليف إضافية
        .Range("J5").Value = "مكتمل"
        
        ' تنسيق الجدول
        .Range("A3:J20").Borders.LineStyle = xlContinuous
        .Columns("A:J").AutoFit
    End With
End Sub

Sub اعداد_جدول_مخزون_المنتجات_التامة(ws As Worksheet)
    ' إعداد جدول مخزون المنتجات التامة
    With ws
        .Range("A1").Value = "مخزون المنتجات التامة"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:H1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "كود المنتج"
        .Range("B3").Value = "اسم المنتج"
        .Range("C3").Value = "الرصيد الحالي"
        .Range("D3").Value = "تكلفة الوحدة"
        .Range("E3").Value = "قيمة المخزون"
        .Range("F3").Value = "سعر البيع"
        .Range("G3").Value = "آخر تحديث"
        .Range("H3").Value = "حالة المخزون"
        
        ' تنسيق العناوين
        .Range("A3:H3").Font.Bold = True
        .Range("A3:H3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:H3").Borders.LineStyle = xlContinuous
        
        ' إضافة صيغة حساب قيمة المخزون
        .Range("E4").Formula = "=C4*D4"
        
        ' إضافة بيانات تجريبية
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("C4").Value = 80
        .Range("D4").Value = 3.5  ' تكلفة الوحدة المحسوبة
        .Range("F4").Value = 12.0
        .Range("G4").Value = Date
        .Range("H4").Value = "متوفر"
        
        .Range("A5").Value = "FG002"
        .Range("B5").Value = "معمول بالجوز"
        .Range("C5").Value = 40
        .Range("D5").Value = 5.35  ' تكلفة الوحدة المحسوبة
        .Range("F5").Value = 8.5
        .Range("G5").Value = Date
        .Range("H5").Value = "متوفر"
        
        .Range("A6").Value = "FG003"
        .Range("B6").Value = "معمول بعين الجمل"
        .Range("C6").Value = 25
        .Range("D6").Value = 5.8   ' تكلفة الوحدة المحسوبة
        .Range("F6").Value = 9.0
        .Range("G6").Value = Date
        .Range("H6").Value = "منخفض"
        
        ' تنسيق الجدول
        .Range("A3:H20").Borders.LineStyle = xlContinuous
        .Columns("A:H").AutoFit
    End With
End Sub

Sub اعداد_جدول_المبيعات(ws As Worksheet)
    ' إعداد جدول المبيعات
    With ws
        .Range("A1").Value = "سجل المبيعات"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:I1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' عناوين الأعمدة
        .Range("A3").Value = "رقم الفاتورة"
        .Range("B3").Value = "التاريخ"
        .Range("C3").Value = "كود العميل"
        .Range("D3").Value = "كود المنتج"
        .Range("E3").Value = "اسم المنتج"
        .Range("F3").Value = "الكمية"
        .Range("G3").Value = "سعر الوحدة"
        .Range("H3").Value = "إجمالي المبيعات"
        .Range("I3").Value = "ملاحظات"
        
        ' تنسيق العناوين
        .Range("A3:I3").Font.Bold = True
        .Range("A3:I3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:I3").Borders.LineStyle = xlContinuous
        
        ' إضافة صيغة حساب إجمالي المبيعات
        .Range("H4").Formula = "=F4*G4"
        
        ' إضافة بيانات تجريبية
        .Range("A4").Value = "SAL001"
        .Range("B4").Value = Date
        .Range("C4").Value = "CUS001"
        .Range("D4").Value = "FG001"
        .Range("E4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("F4").Value = 20
        .Range("G4").Value = 12.0
        .Range("I4").Value = "طلب عادي"
        
        .Range("A5").Value = "SAL002"
        .Range("B5").Value = Date
        .Range("C5").Value = "CUS002"
        .Range("D5").Value = "FG002"
        .Range("E5").Value = "معمول بالجوز"
        .Range("F5").Value = 15
        .Range("G5").Value = 8.5
        .Range("I5").Value = "طلب مطعم"
        
        ' تنسيق الجدول
        .Range("A3:I20").Borders.LineStyle = xlContinuous
        .Columns("A:I").AutoFit
    End With
End Sub

Sub اعداد_تقرير_التكاليف(ws As Worksheet)
    ' إعداد تقرير التكاليف
    With ws
        .Range("A1").Value = "تقرير تكاليف الإنتاج"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        ' قسم تكاليف المواد الخام
        .Range("A3").Value = "تكاليف المواد الخام"
        .Range("A3").Font.Bold = True
        .Range("A3").Interior.Color = RGB(255, 255, 0)
        
        .Range("A4").Value = "كود المنتج"
        .Range("B4").Value = "اسم المنتج"
        .Range("C4").Value = "تكلفة المواد الخام"
        .Range("D4").Value = "تكلفة العمالة"
        .Range("E4").Value = "التكاليف الإضافية"
        .Range("F4").Value = "إجمالي التكلفة"
        
        ' تنسيق العناوين
        .Range("A4:F4").Font.Bold = True
        .Range("A4:F4").Interior.Color = RGB(217, 217, 217)
        .Range("A4:F4").Borders.LineStyle = xlContinuous
        
        ' إضافة بيانات التكاليف
        .Range("A5").Value = "FG001"
        .Range("B5").Value = "زيتون أخضر مشوي باللبنة"
        .Range("C5").Value = 2.75  ' 0.3*5.5 + 0.2*8
        .Range("D5").Value = 0.5   ' تكلفة العمالة للوحدة
        .Range("E5").Value = 0.25  ' تكاليف إضافية للوحدة
        .Range("F5").Formula = "=C5+D5+E5"
        
        .Range("A6").Value = "FG002"
        .Range("B6").Value = "معمول بالجوز"
        .Range("C6").Value = 3.25  ' 0.15*2.5 + 0.08*15 + 0.02*4
        .Range("D6").Value = 1.5   ' تكلفة العمالة للوحدة
        .Range("E6").Value = 0.6   ' تكاليف إضافية للوحدة
        .Range("F6").Formula = "=C6+D6+E6"
        
        .Range("A7").Value = "FG003"
        .Range("B7").Value = "معمول بعين الجمل"
        .Range("C7").Value = 3.82  ' 0.15*2.5 + 0.08*18 + 0.02*4
        .Range("D7").Value = 1.5   ' تكلفة العمالة للوحدة
        .Range("E7").Value = 0.6   ' تكاليف إضافية للوحدة
        .Range("F7").Formula = "=C7+D7+E7"
        
        ' إضافة إجماليات
        .Range("A9").Value = "إجمالي التكاليف:"
        .Range("A9").Font.Bold = True
        .Range("F9").Formula = "=SUM(F5:F7)"
        .Range("F9").Font.Bold = True
        
        ' تنسيق الجدول
        .Range("A4:F9").Borders.LineStyle = xlContinuous
        .Columns("A:F").AutoFit
    End With
End Sub
