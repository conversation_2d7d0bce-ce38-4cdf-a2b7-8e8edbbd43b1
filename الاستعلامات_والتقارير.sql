-- استعلامات وتقارير نظام محاسبة التكاليف
-- مصنع المواد الغذائية

-- ========================================
-- استعلامات أرصدة المخزون
-- ========================================

-- استعلام أرصدة المواد الخام مع تفاصيل كاملة
CREATE VIEW عرض_ارصدة_المواد_الخام_تفصيلي AS
SELECT 
    ر.كود_الرصيد,
    م.كود_المادة,
    م.اسم_المادة,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    و.اسم_الوحدة,
    ر.متوسط_التكلفة,
    ر.القيمة_الاجمالية,
    م.الحد_الادنى,
    م.الحد_الاقصى,
    IIF(ر.الكمية_المتاحة <= م.الحد_الادنى, "تحت الحد الأدنى", 
        IIF(ر.الكمية_المتاحة >= م.الحد_الاقصى, "فوق الحد الأقصى", "طبيعي")) AS حالة_المخزون,
    ر.تاريخ_اخر_تحديث
FROM ((ارصدة_المواد_الخام ر
INNER JOIN المواد_الخام م ON ر.كود_المادة = م.كود_المادة)
INNER JOIN المخازن مخ ON ر.كود_المخزن = مخ.كود_المخزن)
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة
WHERE ر.الكمية_المتاحة > 0
ORDER BY م.اسم_المادة, مخ.اسم_المخزن;

-- استعلام أرصدة المنتجات التامة مع تفاصيل كاملة
CREATE VIEW عرض_ارصدة_المنتجات_التامة_تفصيلي AS
SELECT 
    ر.كود_الرصيد,
    م.كود_المنتج,
    م.اسم_المنتج,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    و.اسم_الوحدة,
    ر.متوسط_التكلفة,
    ر.القيمة_الاجمالية,
    م.سعر_البيع,
    (م.سعر_البيع - ر.متوسط_التكلفة) AS هامش_الربح_للوحدة,
    ((م.سعر_البيع - ر.متوسط_التكلفة) / م.سعر_البيع * 100) AS نسبة_هامش_الربح,
    م.الحد_الادنى,
    م.الحد_الاقصى,
    IIF(ر.الكمية_المتاحة <= م.الحد_الادنى, "تحت الحد الأدنى", 
        IIF(ر.الكمية_المتاحة >= م.الحد_الاقصى, "فوق الحد الأقصى", "طبيعي")) AS حالة_المخزون,
    ر.تاريخ_اخر_تحديث
FROM ((ارصدة_المنتجات_التامة ر
INNER JOIN المنتجات_التامة م ON ر.كود_المنتج = م.كود_المنتج)
INNER JOIN المخازن مخ ON ر.كود_المخزن = مخ.كود_المخزن)
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة
WHERE ر.الكمية_المتاحة > 0
ORDER BY م.اسم_المنتج, مخ.اسم_المخزن;

-- استعلام المواد التي وصلت للحد الأدنى
CREATE VIEW تنبيه_المواد_تحت_الحد_الادنى AS
SELECT 
    م.اسم_المادة,
    مخ.اسم_المخزن,
    ر.الكمية_المتاحة,
    م.الحد_الادنى,
    (م.الحد_الادنى - ر.الكمية_المتاحة) AS الكمية_المطلوبة,
    و.اسم_الوحدة
FROM ((ارصدة_المواد_الخام ر
INNER JOIN المواد_الخام م ON ر.كود_المادة = م.كود_المادة)
INNER JOIN المخازن مخ ON ر.كود_المخزن = مخ.كود_المخزن)
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة
WHERE ر.الكمية_المتاحة <= م.الحد_الادنى AND م.الحد_الادنى > 0
ORDER BY (م.الحد_الادنى - ر.الكمية_المتاحة) DESC;

-- ========================================
-- استعلامات تكاليف الإنتاج
-- ========================================

-- استعلام تفصيلي لتكاليف أوامر الإنتاج
CREATE VIEW عرض_تكاليف_اوامر_الانتاج_تفصيلي AS
SELECT 
    أ.رقم_امر_الانتاج,
    م.اسم_المنتج,
    أ.كمية_الانتاج,
    أ.تاريخ_الامر,
    أ.تاريخ_البدء,
    أ.تاريخ_الانتهاء,
    أ.حالة_الامر,
    مخ.اسم_المخزن,
    أ.تكلفة_المواد_الخام,
    أ.تكلفة_العمالة,
    أ.التكاليف_المباشرة,
    أ.التكاليف_غير_المباشرة,
    أ.اجمالي_التكلفة,
    (أ.اجمالي_التكلفة / أ.كمية_الانتاج) AS تكلفة_الوحدة,
    م.سعر_البيع,
    (م.سعر_البيع - (أ.اجمالي_التكلفة / أ.كمية_الانتاج)) AS هامش_الربح_للوحدة,
    ((م.سعر_البيع - (أ.اجمالي_التكلفة / أ.كمية_الانتاج)) / م.سعر_البيع * 100) AS نسبة_هامش_الربح
FROM ((اوامر_الانتاج أ
INNER JOIN المنتجات_التامة م ON أ.كود_المنتج = م.كود_المنتج)
INNER JOIN المخازن مخ ON أ.كود_المخزن = مخ.كود_المخزن)
ORDER BY أ.تاريخ_الامر DESC;

-- استعلام تحليل التكاليف حسب نوع التكلفة
CREATE VIEW تحليل_التكاليف_حسب_النوع AS
SELECT 
    ت.اسم_نوع_التكلفة,
    ت.تصنيف_التكلفة,
    COUNT(تك.كود_التكلفة) AS عدد_العمليات,
    SUM(تك.قيمة_التكلفة) AS اجمالي_التكلفة,
    AVG(تك.قيمة_التكلفة) AS متوسط_التكلفة,
    MIN(تك.قيمة_التكلفة) AS اقل_تكلفة,
    MAX(تك.قيمة_التكلفة) AS اعلى_تكلفة
FROM انواع_التكاليف ت
LEFT JOIN تكاليف_اوامر_الانتاج تك ON ت.كود_نوع_التكلفة = تك.كود_نوع_التكلفة
GROUP BY ت.اسم_نوع_التكلفة, ت.تصنيف_التكلفة
ORDER BY اجمالي_التكلفة DESC;

-- استعلام تكلفة المواد الخام المستهلكة في الإنتاج
CREATE VIEW تكلفة_المواد_المستهلكة_في_الانتاج AS
SELECT 
    أ.رقم_امر_الانتاج,
    م.اسم_المنتج,
    مخ.اسم_المادة,
    ت.الكمية_المستهلكة,
    ت.سعر_الوحدة,
    ت.القيمة_الاجمالية,
    و.اسم_الوحدة,
    أ.تاريخ_الامر
FROM (((تفاصيل_استهلاك_الانتاج ت
INNER JOIN اوامر_الانتاج أ ON ت.رقم_امر_الانتاج = أ.رقم_امر_الانتاج)
INNER JOIN المنتجات_التامة م ON أ.كود_المنتج = م.كود_المنتج)
INNER JOIN المواد_الخام مخ ON ت.كود_المادة = مخ.كود_المادة)
INNER JOIN الوحدات و ON مخ.كود_الوحدة = و.كود_الوحدة
ORDER BY أ.تاريخ_الامر DESC, ت.القيمة_الاجمالية DESC;

-- ========================================
-- استعلامات المشتريات
-- ========================================

-- استعلام تفصيلي لفواتير المشتريات
CREATE VIEW عرض_فواتير_المشتريات_تفصيلي AS
SELECT 
    ف.رقم_الفاتورة,
    مو.اسم_المورد,
    ف.تاريخ_الفاتورة,
    ف.رقم_فاتورة_المورد,
    ف.اجمالي_الفاتورة,
    ف.الخصم,
    ف.الضريبة,
    ف.صافي_الفاتورة,
    ف.حالة_الفاتورة,
    COUNT(ت.كود_التفصيل) AS عدد_الاصناف,
    SUM(ت.الكمية) AS اجمالي_الكمية
FROM فواتير_المشتريات ف
INNER JOIN الموردين مو ON ف.كود_المورد = مو.كود_المورد
LEFT JOIN تفاصيل_فواتير_المشتريات ت ON ف.رقم_الفاتورة = ت.رقم_الفاتورة
GROUP BY ف.رقم_الفاتورة, مو.اسم_المورد, ف.تاريخ_الفاتورة, ف.رقم_فاتورة_المورد, 
         ف.اجمالي_الفاتورة, ف.الخصم, ف.الضريبة, ف.صافي_الفاتورة, ف.حالة_الفاتورة
ORDER BY ف.تاريخ_الفاتورة DESC;

-- استعلام تحليل المشتريات حسب المورد
CREATE VIEW تحليل_المشتريات_حسب_المورد AS
SELECT 
    مو.اسم_المورد,
    COUNT(ف.رقم_الفاتورة) AS عدد_الفواتير,
    SUM(ف.صافي_الفاتورة) AS اجمالي_المشتريات,
    AVG(ف.صافي_الفاتورة) AS متوسط_قيمة_الفاتورة,
    MIN(ف.تاريخ_الفاتورة) AS اول_فاتورة,
    MAX(ف.تاريخ_الفاتورة) AS اخر_فاتورة
FROM الموردين مو
INNER JOIN فواتير_المشتريات ف ON مو.كود_المورد = ف.كود_المورد
WHERE ف.حالة_الفاتورة = 'مؤكدة'
GROUP BY مو.اسم_المورد
ORDER BY اجمالي_المشتريات DESC;

-- استعلام تحليل المشتريات حسب المادة الخام
CREATE VIEW تحليل_المشتريات_حسب_المادة AS
SELECT 
    م.اسم_المادة,
    و.اسم_الوحدة,
    COUNT(ت.كود_التفصيل) AS عدد_مرات_الشراء,
    SUM(ت.الكمية) AS اجمالي_الكمية_المشتراة,
    SUM(ت.القيمة_الاجمالية) AS اجمالي_قيمة_المشتريات,
    AVG(ت.سعر_الوحدة) AS متوسط_سعر_الشراء,
    MIN(ت.سعر_الوحدة) AS اقل_سعر_شراء,
    MAX(ت.سعر_الوحدة) AS اعلى_سعر_شراء
FROM ((تفاصيل_فواتير_المشتريات ت
INNER JOIN المواد_الخام م ON ت.كود_المادة = م.كود_المادة)
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة)
INNER JOIN فواتير_المشتريات ف ON ت.رقم_الفاتورة = ف.رقم_الفاتورة
WHERE ف.حالة_الفاتورة = 'مؤكدة'
GROUP BY م.اسم_المادة, و.اسم_الوحدة
ORDER BY اجمالي_قيمة_المشتريات DESC;

-- ========================================
-- استعلامات المبيعات
-- ========================================

-- استعلام تفصيلي لفواتير المبيعات
CREATE VIEW عرض_فواتير_المبيعات_تفصيلي AS
SELECT 
    ف.رقم_الفاتورة,
    ع.اسم_العميل,
    ف.تاريخ_الفاتورة,
    ف.اجمالي_الفاتورة,
    ف.الخصم,
    ف.الضريبة,
    ف.صافي_الفاتورة,
    ف.حالة_الفاتورة,
    COUNT(ت.كود_التفصيل) AS عدد_الاصناف,
    SUM(ت.الكمية) AS اجمالي_الكمية,
    SUM(ت.اجمالي_التكلفة) AS اجمالي_تكلفة_البضاعة,
    (ف.صافي_الفاتورة - SUM(ت.اجمالي_التكلفة)) AS اجمالي_الربح,
    ((ف.صافي_الفاتورة - SUM(ت.اجمالي_التكلفة)) / ف.صافي_الفاتورة * 100) AS نسبة_هامش_الربح
FROM فواتير_المبيعات ف
INNER JOIN العملاء ع ON ف.كود_العميل = ع.كود_العميل
LEFT JOIN تفاصيل_فواتير_المبيعات ت ON ف.رقم_الفاتورة = ت.رقم_الفاتورة
GROUP BY ف.رقم_الفاتورة, ع.اسم_العميل, ف.تاريخ_الفاتورة, 
         ف.اجمالي_الفاتورة, ف.الخصم, ف.الضريبة, ف.صافي_الفاتورة, ف.حالة_الفاتورة
ORDER BY ف.تاريخ_الفاتورة DESC;

-- استعلام تحليل المبيعات حسب العميل
CREATE VIEW تحليل_المبيعات_حسب_العميل AS
SELECT 
    ع.اسم_العميل,
    COUNT(ف.رقم_الفاتورة) AS عدد_الفواتير,
    SUM(ف.صافي_الفاتورة) AS اجمالي_المبيعات,
    AVG(ف.صافي_الفاتورة) AS متوسط_قيمة_الفاتورة,
    MIN(ف.تاريخ_الفاتورة) AS اول_فاتورة,
    MAX(ف.تاريخ_الفاتورة) AS اخر_فاتورة
FROM العملاء ع
INNER JOIN فواتير_المبيعات ف ON ع.كود_العميل = ف.كود_العميل
WHERE ف.حالة_الفاتورة = 'مؤكدة'
GROUP BY ع.اسم_العميل
ORDER BY اجمالي_المبيعات DESC;

-- استعلام تحليل المبيعات حسب المنتج
CREATE VIEW تحليل_المبيعات_حسب_المنتج AS
SELECT 
    م.اسم_المنتج,
    و.اسم_الوحدة,
    COUNT(ت.كود_التفصيل) AS عدد_مرات_البيع,
    SUM(ت.الكمية) AS اجمالي_الكمية_المباعة,
    SUM(ت.القيمة_الاجمالية) AS اجمالي_قيمة_المبيعات,
    SUM(ت.اجمالي_التكلفة) AS اجمالي_تكلفة_البضاعة,
    (SUM(ت.القيمة_الاجمالية) - SUM(ت.اجمالي_التكلفة)) AS اجمالي_الربح,
    AVG(ت.سعر_الوحدة) AS متوسط_سعر_البيع,
    AVG(ت.تكلفة_الوحدة) AS متوسط_تكلفة_الوحدة,
    (AVG(ت.سعر_الوحدة) - AVG(ت.تكلفة_الوحدة)) AS متوسط_هامش_الربح
FROM ((تفاصيل_فواتير_المبيعات ت
INNER JOIN المنتجات_التامة م ON ت.كود_المنتج = م.كود_المنتج)
INNER JOIN الوحدات و ON م.كود_الوحدة = و.كود_الوحدة)
INNER JOIN فواتير_المبيعات ف ON ت.رقم_الفاتورة = ف.رقم_الفاتورة
WHERE ف.حالة_الفاتورة = 'مؤكدة'
GROUP BY م.اسم_المنتج, و.اسم_الوحدة
ORDER BY اجمالي_قيمة_المبيعات DESC;

-- ========================================
-- استعلامات حركات المخزون
-- ========================================

-- استعلام تفصيلي لحركات المخزون
CREATE VIEW عرض_حركات_المخزون_تفصيلي AS
SELECT 
    ح.كود_الحركة,
    ح.تاريخ_الحركة,
    ح.نوع_الحركة,
    ح.نوع_المادة,
    IIF(ح.نوع_المادة = 'مادة خام', 
        (SELECT اسم_المادة FROM المواد_الخام WHERE كود_المادة = ح.كود_المادة),
        (SELECT اسم_المنتج FROM المنتجات_التامة WHERE كود_المنتج = ح.كود_المادة)) AS اسم_المادة,
    مخ.اسم_المخزن,
    ح.الكمية,
    ح.سعر_الوحدة,
    ح.القيمة_الاجمالية,
    ح.رقم_المستند,
    ح.نوع_المستند,
    ح.ملاحظات
FROM حركات_المخزون ح
INNER JOIN المخازن مخ ON ح.كود_المخزن = مخ.كود_المخزن
ORDER BY ح.تاريخ_الحركة DESC, ح.كود_الحركة DESC;

-- ========================================
-- استعلامات تقارير الربحية
-- ========================================

-- تقرير الربحية الشامل
CREATE VIEW تقرير_الربحية_الشامل AS
SELECT 
    'المبيعات' AS البند,
    SUM(ف.صافي_الفاتورة) AS القيمة,
    1 AS ترتيب_العرض
FROM فواتير_المبيعات ف
WHERE ف.حالة_الفاتورة = 'مؤكدة'

UNION ALL

SELECT 
    'تكلفة البضاعة المباعة' AS البند,
    -SUM(ت.اجمالي_التكلفة) AS القيمة,
    2 AS ترتيب_العرض
FROM تفاصيل_فواتير_المبيعات ت
INNER JOIN فواتير_المبيعات ف ON ت.رقم_الفاتورة = ف.رقم_الفاتورة
WHERE ف.حالة_الفاتورة = 'مؤكدة'

UNION ALL

SELECT 
    'إجمالي الربح' AS البند,
    (SELECT SUM(ف.صافي_الفاتورة) FROM فواتير_المبيعات ف WHERE ف.حالة_الفاتورة = 'مؤكدة') -
    (SELECT SUM(ت.اجمالي_التكلفة) FROM تفاصيل_فواتير_المبيعات ت 
     INNER JOIN فواتير_المبيعات ف ON ت.رقم_الفاتورة = ف.رقم_الفاتورة 
     WHERE ف.حالة_الفاتورة = 'مؤكدة') AS القيمة,
    3 AS ترتيب_العرض

ORDER BY ترتيب_العرض;
