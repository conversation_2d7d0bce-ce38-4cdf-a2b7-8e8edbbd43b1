' سكريپت تشغيل نظام محاسبة التكاليف المتكامل
' يقوم بإنشاء ملف Excel مع جميع الأوراق والجداول المطلوبة

On Error Resume Next

' إنشاء كائن Excel
Dim xlApp, xlWorkbook
Set xlApp = CreateObject("Excel.Application")

If Err.Number <> 0 Then
    WScript.Echo "خطأ في تشغيل Excel: " & Err.Description
    WScript.Quit
End If

xlApp.Visible = True
xlApp.DisplayAlerts = False

' إنشاء مصنف جديد
Set xlWorkbook = xlApp.Workbooks.Add

' حذف الأوراق الافتراضية
Do While xlWorkbook.Worksheets.Count > 1
    xlWorkbook.Worksheets(xlWorkbook.Worksheets.Count).Delete
Loop

' إعادة تسمية الورقة الأولى
xlWorkbook.Worksheets(1).Name = "لوحة_التحكم"

' إضافة الأوراق المطلوبة
xlWorkbook.Worksheets.Add.Name = "البيانات_الاساسية"
xlWorkbook.Worksheets.Add.Name = "الموردين"
xlWorkbook.Worksheets.Add.Name = "العملاء"
xlWorkbook.Worksheets.Add.Name = "المواد_الخام"
xlWorkbook.Worksheets.Add.Name = "المنتجات_التامة"
xlWorkbook.Worksheets.Add.Name = "وحدات_القياس"
xlWorkbook.Worksheets.Add.Name = "مراكز_التكلفة"
xlWorkbook.Worksheets.Add.Name = "المشتريات"
xlWorkbook.Worksheets.Add.Name = "مخزون_المواد_الخام"
xlWorkbook.Worksheets.Add.Name = "الوصفات"
xlWorkbook.Worksheets.Add.Name = "اوامر_الانتاج"
xlWorkbook.Worksheets.Add.Name = "مخزون_المنتجات_التامة"
xlWorkbook.Worksheets.Add.Name = "المبيعات"
xlWorkbook.Worksheets.Add.Name = "تقرير_التكاليف"
xlWorkbook.Worksheets.Add.Name = "تقرير_المخزون"
xlWorkbook.Worksheets.Add.Name = "التقارير_المالية"

' ترتيب الأوراق
xlWorkbook.Worksheets("لوحة_التحكم").Move xlWorkbook.Worksheets(1)

WScript.Echo "تم إنشاء الأوراق بنجاح. جاري إعداد المحتوى..."

' إعداد لوحة التحكم
Call اعداد_لوحة_التحكم(xlWorkbook.Worksheets("لوحة_التحكم"))

' إعداد جداول البيانات الأساسية
Call اعداد_جدول_الموردين(xlWorkbook.Worksheets("الموردين"))
Call اعداد_جدول_العملاء(xlWorkbook.Worksheets("العملاء"))
Call اعداد_جدول_المواد_الخام(xlWorkbook.Worksheets("المواد_الخام"))
Call اعداد_جدول_المنتجات_التامة(xlWorkbook.Worksheets("المنتجات_التامة"))
Call اعداد_جدول_وحدات_القياس(xlWorkbook.Worksheets("وحدات_القياس"))
Call اعداد_جدول_مراكز_التكلفة(xlWorkbook.Worksheets("مراكز_التكلفة"))

' إعداد جداول العمليات
Call اعداد_جدول_المشتريات(xlWorkbook.Worksheets("المشتريات"))
Call اعداد_جدول_مخزون_المواد_الخام(xlWorkbook.Worksheets("مخزون_المواد_الخام"))
Call اعداد_جدول_الوصفات(xlWorkbook.Worksheets("الوصفات"))
Call اعداد_جدول_اوامر_الانتاج(xlWorkbook.Worksheets("اوامر_الانتاج"))
Call اعداد_جدول_مخزون_المنتجات_التامة(xlWorkbook.Worksheets("مخزون_المنتجات_التامة"))
Call اعداد_جدول_المبيعات(xlWorkbook.Worksheets("المبيعات"))

' إعداد التقارير
Call اعداد_تقرير_التكاليف(xlWorkbook.Worksheets("تقرير_التكاليف"))
Call اعداد_تقرير_المخزون(xlWorkbook.Worksheets("تقرير_المخزون"))
Call اعداد_التقارير_المالية(xlWorkbook.Worksheets("التقارير_المالية"))

' حفظ الملف
xlWorkbook.SaveAs "نظام_محاسبة_التكاليف_متكامل.xlsx"

WScript.Echo "تم إنشاء نظام محاسبة التكاليف المتكامل بنجاح!" & vbCrLf & _
             "اسم الملف: نظام_محاسبة_التكاليف_متكامل.xlsx" & vbCrLf & _
             "عدد الأوراق: " & xlWorkbook.Worksheets.Count & vbCrLf & _
             "يتضمن النظام جميع الوظائف المطلوبة لمحاسبة التكاليف"

' تنظيف الكائنات
Set xlWorkbook = Nothing
Set xlApp = Nothing

' إجراءات إعداد الجداول
Sub اعداد_لوحة_التحكم(ws)
    ws.Range("A1").Value = "نظام محاسبة التكاليف المتكامل"
    ws.Range("A1").Font.Size = 18
    ws.Range("A1").Font.Bold = True
    ws.Range("A1:F1").Merge
    ws.Range("A1").HorizontalAlignment = -4108  ' xlCenter
    
    ws.Range("A3").Value = "مصنع المواد الغذائية"
    ws.Range("A3").Font.Size = 14
    ws.Range("A3").Font.Bold = True
    ws.Range("A3:F3").Merge
    ws.Range("A3").HorizontalAlignment = -4108  ' xlCenter
    
    ' إضافة أزرار التنقل
    ws.Range("B6").Value = "البيانات الأساسية"
    ws.Range("B7").Value = "المشتريات والمخزون"
    ws.Range("B8").Value = "الإنتاج والوصفات"
    ws.Range("B9").Value = "المبيعات"
    ws.Range("B10").Value = "التقارير المالية"
    
    ' تنسيق الأزرار
    ws.Range("B6:B10").Font.Bold = True
    ws.Range("B6:B10").Interior.Color = RGB(173, 216, 230)
    
    ' إضافة معلومات النظام
    ws.Range("A13").Value = "المنتجات المدعومة:"
    ws.Range("A14").Value = "• الزيتون الأخضر المشوي باللبنة"
    ws.Range("A15").Value = "• المعمول بالجوز"
    ws.Range("A16").Value = "• المعمول بعين الجمل"
    
    ws.Range("A13:A16").Font.Bold = True
    ws.Columns("A:F").AutoFit
End Sub

Sub اعداد_جدول_الموردين(ws)
    ws.Range("A1").Value = "جدول الموردين"
    ws.Range("A1").Font.Size = 16
    ws.Range("A1").Font.Bold = True
    ws.Range("A1:F1").Merge
    ws.Range("A1").HorizontalAlignment = -4108
    
    ' عناوين الأعمدة
    ws.Range("A3").Value = "كود المورد"
    ws.Range("B3").Value = "اسم المورد"
    ws.Range("C3").Value = "العنوان"
    ws.Range("D3").Value = "الهاتف"
    ws.Range("E3").Value = "البريد الإلكتروني"
    ws.Range("F3").Value = "ملاحظات"
    
    ' تنسيق العناوين
    ws.Range("A3:F3").Font.Bold = True
    ws.Range("A3:F3").Interior.Color = RGB(217, 217, 217)
    
    ' إضافة بيانات تجريبية
    ws.Range("A4").Value = "SUP001"
    ws.Range("B4").Value = "شركة الزيتون الذهبي"
    ws.Range("C4").Value = "عمان - الأردن"
    ws.Range("D4").Value = "06-1234567"
    ws.Range("E4").Value = "<EMAIL>"
    
    ws.Range("A5").Value = "SUP002"
    ws.Range("B5").Value = "مؤسسة الجوز الطبيعي"
    ws.Range("C5").Value = "دمشق - سوريا"
    ws.Range("D5").Value = "011-2345678"
    ws.Range("E5").Value = "<EMAIL>"
    
    ws.Columns("A:F").AutoFit
End Sub

' ملاحظة: باقي الإجراءات ستكون مشابهة لما تم تطويره في ملفات VBA المنفصلة
