' كود VBA لإنشاء نماذج المبيعات
' نظام محاسبة التكاليف لمصنع المواد الغذائية

Option Compare Database
Option Explicit

' إجراء رئيسي لإنشاء نماذج المبيعات
Public Sub انشاء_نماذج_المبيعات()
    On Error GoTo ErrorHandler
    
    ' إنشاء نماذج المبيعات
    Call انشاء_نموذج_فواتير_المبيعات
    Call انشاء_نموذج_تفاصيل_فواتير_المبيعات
    Call انشاء_نموذج_عرض_المبيعات
    
    MsgBox "تم إنشاء نماذج المبيعات بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء نماذج المبيعات: " & Err.Description, vbCritical, "خطأ"
End Sub

' إنشاء نموذج فواتير المبيعات
Private Sub انشاء_نموذج_فواتير_المبيعات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_فواتير_المبيعات"
    frm.Caption = "فواتير المبيعات"
    frm.RecordSource = "فواتير_المبيعات"
    frm.DefaultView = 0
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = True
    
    ' عنوان النموذج
    Set ctl = CreateControl(frm.Name, acLabel, , , , 0, 0, 12000, 600)
    ctl.Caption = "فواتير المبيعات"
    ctl.FontSize = 18
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(0, 120, 215)
    ctl.ForeColor = RGB(255, 255, 255)
    
    ' رقم الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1000, 1500, 300)
    ctl.Caption = "رقم الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2100, 1000, 1200, 300)
    ctl.ControlSource = "رقم_الفاتورة"
    ctl.Enabled = False
    
    ' العميل
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1400, 1500, 300)
    ctl.Caption = "العميل:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2100, 1400, 3000, 300)
    ctl.ControlSource = "كود_العميل"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_العميل, اسم_العميل FROM العملاء WHERE حالة_العميل = 'نشط' ORDER BY اسم_العميل"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;3000"
    ctl.BoundColumn = 1
    
    ' تاريخ الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 5500, 1000, 1500, 300)
    ctl.Caption = "تاريخ الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7100, 1000, 1200, 300)
    ctl.ControlSource = "تاريخ_الفاتورة"
    ctl.Format = "Short Date"
    ctl.DefaultValue = "Date()"
    
    ' تاريخ التسليم
    Set ctl = CreateControl(frm.Name, acLabel, , , , 8500, 1000, 1500, 300)
    ctl.Caption = "تاريخ التسليم:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 10100, 1000, 1200, 300)
    ctl.ControlSource = "تاريخ_التسليم"
    ctl.Format = "Short Date"
    
    ' المخزن
    Set ctl = CreateControl(frm.Name, acLabel, , , , 5500, 1400, 1500, 300)
    ctl.Caption = "المخزن:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 7100, 1400, 2500, 300)
    ctl.ControlSource = "كود_المخزن"
    ctl.RowSourceType = "Table/Query"
    ctl.RowSource = "SELECT كود_المخزن, اسم_المخزن FROM المخازن WHERE نوع_المخزن = 'منتجات تامة' ORDER BY اسم_المخزن"
    ctl.ColumnCount = 2
    ctl.ColumnWidths = "0;2500"
    ctl.BoundColumn = 1
    
    ' حالة الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 1800, 1500, 300)
    ctl.Caption = "حالة الفاتورة:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 2100, 1800, 1500, 300)
    ctl.ControlSource = "حالة_الفاتورة"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "مسودة;مؤكدة;ملغاة;مرتجعة"
    ctl.DefaultValue = """مسودة"""
    
    ' طريقة الدفع
    Set ctl = CreateControl(frm.Name, acLabel, , , , 3800, 1800, 1500, 300)
    ctl.Caption = "طريقة الدفع:"
    Set ctl = CreateControl(frm.Name, acComboBox, , , , 5400, 1800, 1500, 300)
    ctl.ControlSource = "طريقة_الدفع"
    ctl.RowSourceType = "Value List"
    ctl.RowSource = "نقدي;آجل;شيك;تحويل بنكي"
    
    ' مدة السداد
    Set ctl = CreateControl(frm.Name, acLabel, , , , 7100, 1800, 1500, 300)
    ctl.Caption = "مدة السداد (يوم):"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 8700, 1800, 800, 300)
    ctl.ControlSource = "مدة_السداد"
    ctl.Format = "General Number"
    
    ' قسم المبالغ
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2400, 8000, 400)
    ctl.Caption = "المبالغ"
    ctl.FontSize = 14
    ctl.FontBold = True
    ctl.TextAlign = 2
    ctl.BackColor = RGB(200, 200, 200)
    
    ' إجمالي الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 2900, 1500, 300)
    ctl.Caption = "إجمالي الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2100, 2900, 1500, 300)
    ctl.ControlSource = "اجمالي_الفاتورة"
    ctl.Format = "Currency"
    ctl.Enabled = False
    
    ' الخصم
    Set ctl = CreateControl(frm.Name, acLabel, , , , 3800, 2900, 1000, 300)
    ctl.Caption = "الخصم:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 4900, 2900, 1200, 300)
    ctl.ControlSource = "الخصم"
    ctl.Format = "Currency"
    ctl.DefaultValue = "0"
    
    ' الضريبة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 6300, 2900, 1000, 300)
    ctl.Caption = "الضريبة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 7400, 2900, 1200, 300)
    ctl.ControlSource = "الضريبة"
    ctl.Format = "Currency"
    ctl.DefaultValue = "0"
    
    ' صافي الفاتورة
    Set ctl = CreateControl(frm.Name, acLabel, , , , 8800, 2900, 1200, 300)
    ctl.Caption = "صافي الفاتورة:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 10100, 2900, 1500, 300)
    ctl.ControlSource = "صافي_الفاتورة"
    ctl.Format = "Currency"
    ctl.FontBold = True
    ctl.BackColor = RGB(255, 255, 200)
    ctl.Enabled = False
    
    ' ملاحظات
    Set ctl = CreateControl(frm.Name, acLabel, , , , 500, 3400, 1500, 300)
    ctl.Caption = "ملاحظات:"
    Set ctl = CreateControl(frm.Name, acTextBox, , , , 2100, 3400, 6000, 600)
    ctl.ControlSource = "ملاحظات"
    ctl.EnterKeyBehavior = True
    ctl.ScrollBars = 2
    
    ' نموذج فرعي لتفاصيل الفاتورة
    Set ctl = CreateControl(frm.Name, acSubform, , , , 500, 4200, 10500, 3000)
    ctl.SourceObject = "نموذج_تفاصيل_فواتير_المبيعات"
    ctl.LinkChildFields = "رقم_الفاتورة"
    ctl.LinkMasterFields = "رقم_الفاتورة"
    ctl.Name = "subform_تفاصيل_الفاتورة"
    
    ' أزرار التحكم
    Call اضافة_ازرار_فواتير_المبيعات(frm, 7400)
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج تفاصيل فواتير المبيعات
Private Sub انشاء_نموذج_تفاصيل_فواتير_المبيعات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_تفاصيل_فواتير_المبيعات"
    frm.Caption = "تفاصيل فواتير المبيعات"
    frm.RecordSource = "تفاصيل_فواتير_المبيعات"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = True
    frm.AllowDeletions = True
    frm.AllowEdits = True
    frm.NavigationButtons = False
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إنشاء نموذج عرض المبيعات
Private Sub انشاء_نموذج_عرض_المبيعات()
    Dim frm As Form
    Dim ctl As Control
    
    Set frm = CreateForm()
    frm.Name = "نموذج_عرض_المبيعات"
    frm.Caption = "عرض المبيعات"
    frm.RecordSource = "عرض_فواتير_المبيعات_تفصيلي"
    frm.DefaultView = 2 ' Datasheet
    frm.AllowAdditions = False
    frm.AllowDeletions = False
    frm.AllowEdits = False
    frm.NavigationButtons = True
    
    DoCmd.Save acForm, frm.Name
    DoCmd.Close acForm, frm.Name
End Sub

' إجراء إضافة أزرار التحكم لفواتير المبيعات
Private Sub اضافة_ازرار_فواتير_المبيعات(frm As Form, yPosition As Long)
    Dim ctl As Control
    
    ' زر فاتورة جديدة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 500, yPosition, 1300, 400)
    ctl.Caption = "فاتورة جديدة"
    ctl.Name = "btn_فاتورة_جديدة"
    
    ' زر حفظ الفاتورة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 1900, yPosition, 1300, 400)
    ctl.Caption = "حفظ الفاتورة"
    ctl.Name = "btn_حفظ_الفاتورة"
    
    ' زر تأكيد الفاتورة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 3300, yPosition, 1300, 400)
    ctl.Caption = "تأكيد الفاتورة"
    ctl.Name = "btn_تاكيد_الفاتورة"
    
    ' زر حساب المبالغ
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 4700, yPosition, 1300, 400)
    ctl.Caption = "حساب المبالغ"
    ctl.Name = "btn_حساب_المبالغ"
    
    ' زر طباعة
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 6100, yPosition, 1000, 400)
    ctl.Caption = "طباعة"
    ctl.Name = "btn_طباعة"
    
    ' زر إغلاق
    Set ctl = CreateControl(frm.Name, acCommandButton, , , , 7200, yPosition, 1000, 400)
    ctl.Caption = "إغلاق"
    ctl.Name = "btn_اغلاق"
End Sub

' إجراءات معالجة أحداث فواتير المبيعات
Public Sub حساب_مبالغ_فاتورة_المبيعات(رقم_الفاتورة As Long)
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim اجمالي_الفاتورة As Currency
    Dim الخصم As Currency
    Dim الضريبة As Currency
    Dim صافي_الفاتورة As Currency
    
    Set db = CurrentDb()
    
    ' حساب إجمالي الفاتورة من التفاصيل
    Set rs = db.OpenRecordset("SELECT SUM(القيمة_الاجمالية) AS الاجمالي FROM تفاصيل_فواتير_المبيعات WHERE رقم_الفاتورة = " & رقم_الفاتورة)
    If Not rs.EOF Then
        اجمالي_الفاتورة = Nz(rs!الاجمالي, 0)
    End If
    rs.Close
    
    ' الحصول على الخصم والضريبة
    Set rs = db.OpenRecordset("SELECT الخصم, الضريبة FROM فواتير_المبيعات WHERE رقم_الفاتورة = " & رقم_الفاتورة)
    If Not rs.EOF Then
        الخصم = Nz(rs!الخصم, 0)
        الضريبة = Nz(rs!الضريبة, 0)
    End If
    rs.Close
    
    ' حساب صافي الفاتورة
    صافي_الفاتورة = اجمالي_الفاتورة - الخصم + الضريبة
    
    ' تحديث الفاتورة
    db.Execute "UPDATE فواتير_المبيعات SET اجمالي_الفاتورة = " & اجمالي_الفاتورة & _
              ", صافي_الفاتورة = " & صافي_الفاتورة & _
              " WHERE رقم_الفاتورة = " & رقم_الفاتورة
    
    db.Close
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء حساب مبالغ الفاتورة: " & Err.Description, vbCritical, "خطأ"
    If Not db Is Nothing Then db.Close
End Sub

' إجراء تأكيد فاتورة المبيعات وتحديث المخزون
Public Sub تاكيد_فاتورة_المبيعات(رقم_الفاتورة As Long)
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim كود_المنتج As String
    Dim كود_المخزن As String
    Dim الكمية As Double
    Dim سعر_الوحدة As Currency
    Dim تكلفة_الوحدة As Currency
    
    Set db = CurrentDb()
    
    ' التحقق من حالة الفاتورة
    Set rs = db.OpenRecordset("SELECT حالة_الفاتورة, كود_المخزن FROM فواتير_المبيعات WHERE رقم_الفاتورة = " & رقم_الفاتورة)
    If rs.EOF Then
        MsgBox "الفاتورة غير موجودة!", vbExclamation, "تنبيه"
        rs.Close
        db.Close
        Exit Sub
    End If
    
    If rs!حالة_الفاتورة = "مؤكدة" Then
        MsgBox "الفاتورة مؤكدة مسبقاً!", vbExclamation, "تنبيه"
        rs.Close
        db.Close
        Exit Sub
    End If
    
    كود_المخزن = rs!كود_المخزن
    rs.Close
    
    ' معالجة تفاصيل الفاتورة
    Set rs = db.OpenRecordset("SELECT * FROM تفاصيل_فواتير_المبيعات WHERE رقم_الفاتورة = " & رقم_الفاتورة)
    
    While Not rs.EOF
        كود_المنتج = rs!كود_المنتج
        الكمية = rs!الكمية
        سعر_الوحدة = rs!سعر_الوحدة
        تكلفة_الوحدة = rs!تكلفة_الوحدة
        
        ' تحديث رصيد المنتج في المخزن
        Call تحديث_رصيد_منتج_تام(كود_المنتج, كود_المخزن, -الكمية, تكلفة_الوحدة)
        
        ' إضافة حركة مخزون
        Call اضافة_حركة_مخزون("منتج تام", كود_المنتج, كود_المخزن, "صرف", الكمية, سعر_الوحدة, رقم_الفاتورة, "فاتورة مبيعات", "صرف للعميل")
        
        rs.MoveNext
    Wend
    rs.Close
    
    ' تحديث حالة الفاتورة
    db.Execute "UPDATE فواتير_المبيعات SET حالة_الفاتورة = 'مؤكدة' WHERE رقم_الفاتورة = " & رقم_الفاتورة
    
    db.Close
    
    MsgBox "تم تأكيد الفاتورة وتحديث المخزون بنجاح!", vbInformation, "نجح"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تأكيد الفاتورة: " & Err.Description, vbCritical, "خطأ"
    If Not db Is Nothing Then db.Close
End Sub

' إجراء تحديث رصيد المنتج التام
Private Sub تحديث_رصيد_منتج_تام(كود_المنتج As String, كود_المخزن As String, الكمية As Double, تكلفة_الوحدة As Currency)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim الكمية_الحالية As Double
    Dim القيمة_الحالية As Currency
    Dim متوسط_التكلفة_الجديد As Currency
    Dim القيمة_الجديدة As Currency
    
    Set db = CurrentDb()
    
    ' البحث عن الرصيد الحالي
    Set rs = db.OpenRecordset("SELECT * FROM ارصدة_المنتجات_التامة WHERE كود_المنتج = '" & كود_المنتج & "' AND كود_المخزن = '" & كود_المخزن & "'")
    
    If rs.EOF Then
        ' إنشاء رصيد جديد إذا لم يكن موجوداً
        rs.AddNew
        rs!كود_المنتج = كود_المنتج
        rs!كود_المخزن = كود_المخزن
        rs!الكمية_المتاحة = الكمية
        rs!متوسط_التكلفة = تكلفة_الوحدة
        rs!القيمة_الاجمالية = الكمية * تكلفة_الوحدة
        rs!تاريخ_اخر_تحديث = Now()
        rs.Update
    Else
        ' تحديث الرصيد الموجود
        الكمية_الحالية = rs!الكمية_المتاحة
        القيمة_الحالية = rs!القيمة_الاجمالية
        
        ' حساب المتوسط المرجح للتكلفة
        If (الكمية_الحالية + الكمية) <> 0 Then
            متوسط_التكلفة_الجديد = (القيمة_الحالية + (الكمية * تكلفة_الوحدة)) / (الكمية_الحالية + الكمية)
        Else
            متوسط_التكلفة_الجديد = 0
        End If
        
        القيمة_الجديدة = (الكمية_الحالية + الكمية) * متوسط_التكلفة_الجديد
        
        rs.Edit
        rs!الكمية_المتاحة = الكمية_الحالية + الكمية
        rs!متوسط_التكلفة = متوسط_التكلفة_الجديد
        rs!القيمة_الاجمالية = القيمة_الجديدة
        rs!تاريخ_اخر_تحديث = Now()
        rs.Update
    End If
    
    rs.Close
    db.Close
End Sub
