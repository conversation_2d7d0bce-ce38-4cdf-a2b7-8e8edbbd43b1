On Error Resume Next

Dim xlApp
Set xlApp = CreateObject("Excel.Application")

If Err.Number <> 0 Then
    WScript.Echo "خطأ في إنشاء Excel: " & Err.Description
    WScript.Quit
End If

xlApp.Visible = True
Dim xlWorkbook
Set xlWorkbook = xlApp.Workbooks.Add

xlWorkbook.Worksheets(1).Name = "لوحة_التحكم"

Dim filePath
filePath = "نظام_محاسبة_التكاليف.xlsx"

xlWorkbook.SaveAs filePath

WScript.Echo "تم إنشاء الملف بنجاح"

Set xlWorkbook = Nothing
Set xlApp = Nothing
