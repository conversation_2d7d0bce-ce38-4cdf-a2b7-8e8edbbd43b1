Sub انشاء_نظام_محاسبة_التكاليف()
    ' إعداد نظام محاسبة التكاليف المتكامل لمصنع المواد الغذائية
    
    Dim wb As Workbook
    Dim ws As Worksheet
    
    ' إنشاء مصنف جديد
    Set wb = Workbooks.Add
    
    ' حذف الأوراق الافتراضية
    Application.DisplayAlerts = False
    Do While wb.Worksheets.Count > 1
        wb.Worksheets(wb.Worksheets.Count).Delete
    Loop
    Application.DisplayAlerts = True
    
    ' إعادة تسمية الورقة الأولى
    wb.Worksheets(1).Name = "لوحة_التحكم"
    
    ' إضافة الأوراق المطلوبة
    wb.Worksheets.Add.Name = "البيانات_الاساسية"
    wb.Worksheets.Add.Name = "الموردين"
    wb.Worksheets.Add.Name = "العملاء"
    wb.Worksheets.Add.Name = "المواد_الخام"
    wb.Worksheets.Add.Name = "المنتجات_التامة"
    wb.Worksheets.Add.Name = "وحدات_القياس"
    wb.Worksheets.Add.Name = "مراكز_التكلفة"
    wb.Worksheets.Add.Name = "المشتريات"
    wb.Worksheets.Add.Name = "مخزون_المواد_الخام"
    wb.Worksheets.Add.Name = "الوصفات"
    wb.Worksheets.Add.Name = "اوامر_الانتاج"
    wb.Worksheets.Add.Name = "مخزون_المنتجات_التامة"
    wb.Worksheets.Add.Name = "المبيعات"
    wb.Worksheets.Add.Name = "تقرير_التكاليف"
    wb.Worksheets.Add.Name = "تقرير_المخزون"
    wb.Worksheets.Add.Name = "التقارير_المالية"
    
    ' ترتيب الأوراق
    wb.Worksheets("لوحة_التحكم").Move Before:=wb.Worksheets(1)
    
    ' إعداد لوحة التحكم الرئيسية
    Call اعداد_لوحة_التحكم(wb.Worksheets("لوحة_التحكم"))
    
    ' إعداد جداول البيانات الأساسية
    Call اعداد_جدول_الموردين(wb.Worksheets("الموردين"))
    Call اعداد_جدول_العملاء(wb.Worksheets("العملاء"))
    Call اعداد_جدول_المواد_الخام(wb.Worksheets("المواد_الخام"))
    Call اعداد_جدول_المنتجات_التامة(wb.Worksheets("المنتجات_التامة"))
    Call اعداد_جدول_وحدات_القياس(wb.Worksheets("وحدات_القياس"))
    Call اعداد_جدول_مراكز_التكلفة(wb.Worksheets("مراكز_التكلفة"))
    
    ' إعداد جداول العمليات
    Call اعداد_جدول_المشتريات(wb.Worksheets("المشتريات"))
    Call اعداد_جدول_مخزون_المواد_الخام(wb.Worksheets("مخزون_المواد_الخام"))
    Call اعداد_جدول_الوصفات(wb.Worksheets("الوصفات"))
    Call اعداد_جدول_اوامر_الانتاج(wb.Worksheets("اوامر_الانتاج"))
    Call اعداد_جدول_مخزون_المنتجات_التامة(wb.Worksheets("مخزون_المنتجات_التامة"))
    Call اعداد_جدول_المبيعات(wb.Worksheets("المبيعات"))
    
    ' إعداد التقارير
    Call اعداد_تقرير_التكاليف(wb.Worksheets("تقرير_التكاليف"))
    Call اعداد_تقرير_المخزون(wb.Worksheets("تقرير_المخزون"))
    Call اعداد_التقارير_المالية(wb.Worksheets("التقارير_المالية"))
    
    ' حفظ الملف
    wb.SaveAs "نظام_محاسبة_التكاليف_متكامل.xlsx"
    
    MsgBox "تم إنشاء نظام محاسبة التكاليف بنجاح!", vbInformation, "نظام محاسبة التكاليف"
    
End Sub

Sub اعداد_لوحة_التحكم(ws As Worksheet)
    ' إعداد لوحة التحكم الرئيسية
    With ws
        .Range("A1").Value = "نظام محاسبة التكاليف المتكامل"
        .Range("A1").Font.Size = 18
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        
        .Range("A3").Value = "مصنع المواد الغذائية"
        .Range("A3").Font.Size = 14
        .Range("A3").Font.Bold = True
        .Range("A3:F3").Merge
        .Range("A3").HorizontalAlignment = xlCenter
        
        ' إضافة أزرار التنقل
        .Range("B6").Value = "البيانات الأساسية"
        .Range("B7").Value = "المشتريات والمخزون"
        .Range("B8").Value = "الإنتاج والوصفات"
        .Range("B9").Value = "المبيعات"
        .Range("B10").Value = "التقارير المالية"
        
        ' تنسيق الأزرار
        .Range("B6:B10").Font.Bold = True
        .Range("B6:B10").Interior.Color = RGB(173, 216, 230)
        .Range("B6:B10").Borders.LineStyle = xlContinuous
        
        ' إضافة معلومات النظام
        .Range("A13").Value = "المنتجات المدعومة:"
        .Range("A14").Value = "• الزيتون الأخضر المشوي باللبنة"
        .Range("A15").Value = "• المعمول بالجوز"
        .Range("A16").Value = "• المعمول بعين الجمل"
        
        .Range("A13:A16").Font.Bold = True
        
        ' تنسيق عام
        .Columns("A:F").AutoFit
    End With
End Sub

Sub اعداد_جدول_الموردين(ws As Worksheet)
    ' إعداد جدول الموردين
    With ws
        .Range("A1").Value = "جدول الموردين"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' عناوين الأعمدة
        .Range("A3").Value = "كود المورد"
        .Range("B3").Value = "اسم المورد"
        .Range("C3").Value = "العنوان"
        .Range("D3").Value = "الهاتف"
        .Range("E3").Value = "البريد الإلكتروني"
        .Range("F3").Value = "ملاحظات"

        ' تنسيق العناوين
        .Range("A3:F3").Font.Bold = True
        .Range("A3:F3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:F3").Borders.LineStyle = xlContinuous

        ' إضافة بيانات تجريبية
        .Range("A4").Value = "SUP001"
        .Range("B4").Value = "شركة الزيتون الذهبي"
        .Range("C4").Value = "عمان - الأردن"
        .Range("D4").Value = "06-1234567"
        .Range("E4").Value = "<EMAIL>"

        .Range("A5").Value = "SUP002"
        .Range("B5").Value = "مؤسسة الجوز الطبيعي"
        .Range("C5").Value = "دمشق - سوريا"
        .Range("D5").Value = "011-2345678"
        .Range("E5").Value = "<EMAIL>"

        .Range("A6").Value = "SUP003"
        .Range("B6").Value = "شركة اللبنة الطازجة"
        .Range("C6").Value = "بيروت - لبنان"
        .Range("D6").Value = "01-345678"
        .Range("E6").Value = "<EMAIL>"

        ' تنسيق الجدول
        .Range("A3:F10").Borders.LineStyle = xlContinuous
        .Columns("A:F").AutoFit
    End With
End Sub

Sub اعداد_جدول_العملاء(ws As Worksheet)
    ' إعداد جدول العملاء
    With ws
        .Range("A1").Value = "جدول العملاء"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' عناوين الأعمدة
        .Range("A3").Value = "كود العميل"
        .Range("B3").Value = "اسم العميل"
        .Range("C3").Value = "العنوان"
        .Range("D3").Value = "الهاتف"
        .Range("E3").Value = "البريد الإلكتروني"
        .Range("F3").Value = "نوع العميل"

        ' تنسيق العناوين
        .Range("A3:F3").Font.Bold = True
        .Range("A3:F3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:F3").Borders.LineStyle = xlContinuous

        ' إضافة بيانات تجريبية
        .Range("A4").Value = "CUS001"
        .Range("B4").Value = "سوبر ماركت الأمل"
        .Range("C4").Value = "عمان - الأردن"
        .Range("D4").Value = "06-7654321"
        .Range("E4").Value = "<EMAIL>"
        .Range("F4").Value = "تجزئة"

        .Range("A5").Value = "CUS002"
        .Range("B5").Value = "مطعم الأصالة"
        .Range("C5").Value = "دبي - الإمارات"
        .Range("D5").Value = "04-8765432"
        .Range("E5").Value = "<EMAIL>"
        .Range("F5").Value = "مطاعم"

        ' تنسيق الجدول
        .Range("A3:F10").Borders.LineStyle = xlContinuous
        .Columns("A:F").AutoFit
    End With
End Sub

Sub اعداد_جدول_المواد_الخام(ws As Worksheet)
    ' إعداد جدول المواد الخام
    With ws
        .Range("A1").Value = "جدول المواد الخام"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:G1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' عناوين الأعمدة
        .Range("A3").Value = "كود المادة"
        .Range("B3").Value = "اسم المادة"
        .Range("C3").Value = "وحدة القياس"
        .Range("D3").Value = "السعر الوحدة"
        .Range("E3").Value = "الحد الأدنى"
        .Range("F3").Value = "المورد الرئيسي"
        .Range("G3").Value = "ملاحظات"

        ' تنسيق العناوين
        .Range("A3:G3").Font.Bold = True
        .Range("A3:G3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:G3").Borders.LineStyle = xlContinuous

        ' إضافة بيانات تجريبية للمواد الخام
        .Range("A4").Value = "RM001"
        .Range("B4").Value = "زيتون أخضر"
        .Range("C4").Value = "كيلوغرام"
        .Range("D4").Value = 5.5
        .Range("E4").Value = 100
        .Range("F4").Value = "SUP001"
        .Range("G4").Value = "زيتون طازج عالي الجودة"

        .Range("A5").Value = "RM002"
        .Range("B5").Value = "لبنة طبيعية"
        .Range("C5").Value = "كيلوغرام"
        .Range("D5").Value = 8.0
        .Range("E5").Value = 50
        .Range("F5").Value = "SUP003"
        .Range("G5").Value = "لبنة طازجة خالية من المواد الحافظة"

        .Range("A6").Value = "RM003"
        .Range("B6").Value = "جوز مقشر"
        .Range("C6").Value = "كيلوغرام"
        .Range("D6").Value = 15.0
        .Range("E6").Value = 25
        .Range("F6").Value = "SUP002"
        .Range("G6").Value = "جوز طبيعي مقشر"

        .Range("A7").Value = "RM004"
        .Range("B7").Value = "عين الجمل"
        .Range("C7").Value = "كيلوغرام"
        .Range("D7").Value = 18.0
        .Range("E7").Value = 20
        .Range("F7").Value = "SUP002"
        .Range("G7").Value = "عين جمل طبيعي عالي الجودة"

        .Range("A8").Value = "RM005"
        .Range("B8").Value = "دقيق أبيض"
        .Range("C8").Value = "كيلوغرام"
        .Range("D8").Value = 2.5
        .Range("E8").Value = 200
        .Range("F8").Value = "SUP001"
        .Range("G8").Value = "دقيق ممتاز للمعمول"

        .Range("A9").Value = "RM006"
        .Range("B9").Value = "سمن نباتي"
        .Range("C9").Value = "كيلوغرام"
        .Range("D9").Value = 4.0
        .Range("E9").Value = 50
        .Range("F9").Value = "SUP001"
        .Range("G9").Value = "سمن نباتي للخبز"

        .Range("A10").Value = "RM007"
        .Range("B10").Value = "سكر ناعم"
        .Range("C10").Value = "كيلوغرام"
        .Range("D10").Value = 3.0
        .Range("E10").Value = 100
        .Range("F10").Value = "SUP001"
        .Range("G10").Value = "سكر أبيض ناعم"

        ' تنسيق الجدول
        .Range("A3:G15").Borders.LineStyle = xlContinuous
        .Columns("A:G").AutoFit
    End With
End Sub

Sub اعداد_جدول_المنتجات_التامة(ws As Worksheet)
    ' إعداد جدول المنتجات التامة
    With ws
        .Range("A1").Value = "جدول المنتجات التامة"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' عناوين الأعمدة
        .Range("A3").Value = "كود المنتج"
        .Range("B3").Value = "اسم المنتج"
        .Range("C3").Value = "وحدة القياس"
        .Range("D3").Value = "سعر البيع"
        .Range("E3").Value = "مدة الصلاحية (يوم)"
        .Range("F3").Value = "ملاحظات"

        ' تنسيق العناوين
        .Range("A3:F3").Font.Bold = True
        .Range("A3:F3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:F3").Borders.LineStyle = xlContinuous

        ' إضافة المنتجات المستهدفة
        .Range("A4").Value = "FG001"
        .Range("B4").Value = "زيتون أخضر مشوي باللبنة"
        .Range("C4").Value = "علبة 500 غرام"
        .Range("D4").Value = 12.0
        .Range("E4").Value = 30
        .Range("F4").Value = "منتج مميز للإفطار"

        .Range("A5").Value = "FG002"
        .Range("B5").Value = "معمول بالجوز"
        .Range("C5").Value = "علبة 250 غرام"
        .Range("D5").Value = 8.5
        .Range("E5").Value = 15
        .Range("F5").Value = "حلويات شرقية تقليدية"

        .Range("A6").Value = "FG003"
        .Range("B6").Value = "معمول بعين الجمل"
        .Range("C6").Value = "علبة 250 غرام"
        .Range("D6").Value = 9.0
        .Range("E6").Value = 15
        .Range("F6").Value = "حلويات شرقية فاخرة"

        ' تنسيق الجدول
        .Range("A3:F10").Borders.LineStyle = xlContinuous
        .Columns("A:F").AutoFit
    End With
End Sub

Sub اعداد_جدول_وحدات_القياس(ws As Worksheet)
    ' إعداد جدول وحدات القياس
    With ws
        .Range("A1").Value = "جدول وحدات القياس"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:C1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' عناوين الأعمدة
        .Range("A3").Value = "كود الوحدة"
        .Range("B3").Value = "اسم الوحدة"
        .Range("C3").Value = "ملاحظات"

        ' تنسيق العناوين
        .Range("A3:C3").Font.Bold = True
        .Range("A3:C3").Interior.Color = RGB(217, 217, 217)
        .Range("A3:C3").Borders.LineStyle = xlContinuous

        ' إضافة وحدات القياس
        .Range("A4").Value = "KG"
        .Range("B4").Value = "كيلوغرام"
        .Range("C4").Value = "وحدة الوزن الأساسية"

        .Range("A5").Value = "GM"
        .Range("B5").Value = "غرام"
        .Range("C5").Value = "وحدة وزن فرعية"

        .Range("A6").Value = "PC"
        .Range("B6").Value = "قطعة"
        .Range("C6").Value = "وحدة العد"

        .Range("A7").Value = "BOX"
        .Range("B7").Value = "علبة"
        .Range("C7").Value = "وحدة التعبئة"

        .Range("A8").Value = "LT"
        .Range("B8").Value = "لتر"
        .Range("C8").Value = "وحدة الحجم"

        ' تنسيق الجدول
        .Range("A3:C10").Borders.LineStyle = xlContinuous
        .Columns("A:C").AutoFit
    End With
End Sub
